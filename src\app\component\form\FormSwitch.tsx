import { useField, useFormikContext } from "formik";
import React from "react";
import RxSwitch from "../RxSwitch";
import FormErrorMessage from "./FormErrorMessage";

interface FormSwitchProps {
  name: string;
  label?: string;
  disabled?: boolean;
  className?: string;
  size?: "sm" | "lg";
  onChange?: (checked: boolean) => void;
}

const FormSwitch: React.FC<FormSwitchProps> = ({
  name,
  label,
  disabled = false,
  className = "",
  size,
  onChange,
}) => {
  const [field] = useField(name);
  const { setFieldValue, setFieldTouched } = useFormikContext();

  const handleChange = (checked: boolean) => {
    setFieldValue(name, checked);
    setFieldTouched(name, true);
    if (onChange) {
      onChange(checked);
    }
  };

  return (
    <div>
      <RxSwitch
        id={name}
        checked={field.value || false}
        onChange={handleChange}
        disabled={disabled}
        className={className}
        label={label}
        size={size}
      />
      <FormErrorMessage name={name} />
    </div>
  );
};

export default FormSwitch;
