import React, { useMemo } from "react";
import Select, { Props as SelectProps, ActionMeta } from "react-select";
import Creatable from "react-select/creatable";
import {
  getSelectStyles,
  getSelectTheme,
  mergeReactSelectStyle,
} from "../utils/CommonUtils";

export interface RxReactSelectProps extends SelectProps {
  isCreatable?: boolean; // <- New prop
}

const SELECT_ALL_VALUE = "__SELECT_ALL__";
const SELECT_ALL_LABEL = "Select All";

const RxReactSelect: React.FC<RxReactSelectProps> = ({
  styles,
  isCreatable = false,
  options = [],
  isMulti,
  value,
  onChange,
  ...rest
}) => {
  const finalStyles = mergeReactSelectStyle(getSelectStyles(), styles || {});
  const SelectComponent = isCreatable ? Creatable : Select;

  // Memoize options with Select All
  const optionsWithSelectAll = useMemo(() => {
    if (!isMulti || !Array.isArray(options) || options.length === 0)
      return options;
    // Avoid duplicate Select All
    if ((options as any)[0]?.value === SELECT_ALL_VALUE) return options;
    return [{ label: SELECT_ALL_LABEL, value: SELECT_ALL_VALUE }, ...options];
  }, [options, isMulti]);

  // Helper to get all option values
  const getAllOptionValues = () =>
    optionsWithSelectAll.filter((opt: any) => opt.value !== SELECT_ALL_VALUE);

  // Determine if Select All should be selected
  const isAllSelected = () => {
    if (!isMulti) return false;
    const allOptions = getAllOptionValues();
    if (!Array.isArray(value)) return false;
    return (
      value.length === allOptions.length &&
      allOptions.every((opt) => value.some((v: any) => v.value === opt.value))
    );
  };

  // Compute value for Select
  const computedValue = useMemo(() => {
    if (!isMulti) return value;
    if (!Array.isArray(value)) return value;
    // Only show actual options as selected, never Select All
    return getAllOptionValues().filter((opt) =>
      value.some((v: any) => v.value === opt.value)
    );
  }, [value, isMulti, optionsWithSelectAll]);

  // Custom onChange handler
  const handleChange = (selected: any, actionMeta: ActionMeta<any>) => {
    if (!isMulti) {
      onChange && onChange(selected, actionMeta);
      return;
    }
    let selectedArray = Array.isArray(selected) ? selected : [];
    // If Select All is selected
    if (selectedArray.some((v) => v.value === SELECT_ALL_VALUE)) {
      // If already all selected, deselect all
      if (isAllSelected()) {
        onChange && onChange([], actionMeta);
      } else {
        onChange && onChange(getAllOptionValues(), actionMeta);
      }
      return;
    }
    // If all options are selected manually
    if (
      selectedArray.length === getAllOptionValues().length &&
      getAllOptionValues().every((opt) =>
        selectedArray.some((v) => v.value === opt.value)
      )
    ) {
      onChange && onChange(getAllOptionValues(), actionMeta);
      return;
    }
    // Otherwise, just update as normal (removing Select All if present)
    onChange &&
      onChange(
        selectedArray.filter((v: any) => v.value !== SELECT_ALL_VALUE),
        actionMeta
      );
  };

  // Always show all options in dropdown, even if selected
  const menuOptions = optionsWithSelectAll;

  // // Custom Option component to add checkbox only for multi-select
  // const Option = (props: any) => {
  //   const { isSelected, isFocused, label, innerProps, innerRef, data } = props;

  //   // For Select All, check if all options are selected
  //   const checked =
  //     data.value === SELECT_ALL_VALUE ? isAllSelected() : isSelected;

  //   return (
  //     <div
  //       ref={innerRef}
  //       {...innerProps}
  //       className={`custom-option ${isFocused ? "focused" : ""} ${
  //         isSelected ? "selected" : ""
  //       }`}
  //       style={{
  //         display: "flex",
  //         alignItems: "center",
  //         padding: "8px 12px",
  //         cursor: "pointer",
  //       }}
  //     >
  //       {isMulti && (
  //         <input
  //           type="checkbox"
  //           checked={checked}
  //           readOnly
  //           style={{ marginRight: 8 }}
  //         />
  //       )}
  //       <span>{label}</span>
  //     </div>
  //   );
  // };

  // Custom Option component to add checkbox only for multi-select
  const Option = (props: any) => {
    const { isSelected, isFocused, label, innerProps, innerRef, data } = props;

    // For Select All, check if all options are selected
    const checked =
      data.value === SELECT_ALL_VALUE ? isAllSelected() : isSelected;

    // Build CSS classes for proper styling
    const cssClasses = [
      "select__option",
      isSelected ? "select__option--is-selected" : "",
      isFocused ? "select__option--is-focused" : "",
      isMulti ? "select__option--is-multi" : "",
    ]
      .filter(Boolean)
      .join(" ");

    return (
      <div
        ref={innerRef}
        {...innerProps}
        className={cssClasses}
        style={{
          display: "flex",
          alignItems: "center",
          padding: "8px 12px",
          cursor: "pointer",
        }}
      >
        {isMulti && (
          <input
            type="checkbox"
            checked={checked}
            readOnly
            style={{ marginRight: 8 }}
          />
        )}
        <span>{label}</span>
      </div>
    );
  };

  return (
    <SelectComponent
      isMulti={isMulti}
      options={menuOptions}
      value={computedValue}
      onChange={handleChange}
      classNamePrefix="select"
      styles={finalStyles}
      theme={getSelectTheme()}
      closeMenuOnSelect={isMulti ? false : true}
      hideSelectedOptions={false}
      components={{ Option }}
      {...rest}
    />
  );
};

export default RxReactSelect;
