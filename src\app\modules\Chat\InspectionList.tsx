import React, { FC, useEffect, useMemo, useCallback } from "react";
import { Col, Row } from "react-bootstrap";
import { useGetInspectionListMutation } from "../../apis/inspectionListAPI";
import { DataStatusWrapper } from "../../component";
import { ListType } from "./chatType";
import { useAutoClickFirstItem } from "../../shared/hooks/useAutoClickFirstItem";
import { gotoAutoNext } from "./services/helper";

interface Props {
  search: string;
  selectedInspectionId: string;
  payload?: {
    properties?: string[];
    departments?: string[];
    [key: string]: any;
  };
  onListClick: (type: ListType, item: any) => void;
}

const InspectionList: FC<Props> = ({
  search,
  selectedInspectionId,
  payload,
  onListClick,
}) => {
  const [getInspectionList, { data, isLoading, error }] = useGetInspectionListMutation();

  // Memoize the properties and departments arrays to prevent unnecessary rerenders
  const properties = useMemo(() => payload?.properties || [], [payload?.properties ? payload.properties.join(',') : '']);
  const departments = useMemo(() => payload?.departments || [], [payload?.departments ? payload.departments.join(',') : '']);

  // Memoize the API payload to prevent unnecessary API calls
  const apiPayload = useMemo(() => ({
    search,
    page: 0,
    size: 300,
    properties,
    departments,
    // Include other required fields with default empty values
    inspectionTemplates: [],
    status: [],
    priorities: [],
    inspectors: [],
    ticketStatus: [],
    fromdate: "",
    todate: "",
  }), [search, properties, departments]);

  useEffect(() => {
    getInspectionList(apiPayload);
  }, [getInspectionList, apiPayload]);

  // Memoize the click handler to prevent unnecessary rerenders
  const handleRadioChange = useCallback((item: any) => {
    onListClick("inspection", item);
  }, [onListClick]);

  const ref = useAutoClickFirstItem(
    data?.data?.data,
    !isLoading && data?.data?.data?.length === 1 && !search,
    [data?.data?.data, isLoading],
    gotoAutoNext
  );

  // Memoize the list items to prevent unnecessary rerenders
  const listItems = useMemo(() => {
    return data?.data?.data?.map((item: any, index: number) => (
      <li
        className="d-flex justify-content-between align-items-center border-top p-3 cursor-pointer"
        onClick={() =>
          handleRadioChange({
            label: item.inspectionTitle,
            value: item.inspectionId,
          })
        }
        key={item.inspectionId}
        ref={(el) => (ref.current[index] = el)}
      >
        <label htmlFor={item.inspectionId} className="cursor-pointer">
          <p className="mb-1 fw-bold">{item.inspectionTitle}</p>
          <span className="text-muted fs-12px">
            {item.propertyName}
          </span>
        </label>
        <input
          type="radio"
          name="radio"
          id={item.inspectionId}
          checked={selectedInspectionId === item.inspectionId}
          onChange={() =>
            handleRadioChange({
              label: item.inspectionTitle,
              value: item.inspectionId,
            })
          }
          tabIndex={1}
        />
      </li>
    ));
  }, [data?.data?.data, handleRadioChange, selectedInspectionId, ref]);

  return (
    <div>
      <Row>
        <Col sm={12}>
          <ul className="mt-5 ps-0">
            <DataStatusWrapper
              isLoading={isLoading}
              renderNodata={!data?.data?.data?.length}
              error={!!error}
            >
              {listItems}
            </DataStatusWrapper>
          </ul>
        </Col>
      </Row>
    </div>
  );
};

// Memoize the entire component to prevent unnecessary rerenders
export default React.memo(InspectionList);
