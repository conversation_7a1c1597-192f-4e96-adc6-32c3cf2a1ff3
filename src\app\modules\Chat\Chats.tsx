import { FC, useEffect, useMemo, useRef } from "react";
import { useDispatch } from "react-redux";
import { ClipLoader } from "react-spinners";
import { useGetRoomDetailsMutation } from "../../apis/roomDetailsAPI";
import {
  setThreadinitiatMessage,
  setLoader,
} from "../../apis/slices/socketSlice";
import { Loader, RxInfiniteScroll } from "../../component";
import { useAppSelector } from "../../redux/store";
import {
  ChatHistoryItem,
  ChatThreadHistoryItem,
  ReactionType,
  ReadMessagePayload,
  RoomsList,
} from "./chatType";
import ConversationHistory from "./conversation/ConversationHistory";
import FileList from "./conversation/FileList";
import useConversation from "./conversation/hooks/useConversation";
import InputBox from "./conversation/InputBox";
import ReplyToCard from "./conversation/ReplyToCard";
import { useChat } from "./services/useChat";
import { IoCloseOutline } from "react-icons/io5";
import ConversationThreadHistory from "./conversation/ConversationThreadHistory";
import { isLegalUser } from "./services/helper";

interface Props {
  activeRoom: RoomsList;
  chatHistory: ChatHistoryItem[];
  threadInitiatMessage: ChatThreadHistoryItem;
  chatThreadHistory: ChatThreadHistoryItem[];
  onThreadClose: () => void;
  handleThread: (message: ChatHistoryItem) => void;
  openThread: boolean;
  showFilesOption?: boolean;
  showRecorder?: boolean;
  showRecorderTicket?: boolean;
  showFilesOptionTicket?: boolean;
  hideMainLoader?: boolean;
  toggleFileOption: () => void;
  handleAttachmentClose: () => void;
  handleToggleRecorder: () => void;
  toggleEmojiPicker: () => void;
  showEmojiPicker: boolean;
}

const Chats: FC<Props> = ({
  activeRoom,
  chatHistory,
  threadInitiatMessage,
  chatThreadHistory,
  onThreadClose,
  handleThread,
  openThread,
  showFilesOption,
  showRecorder,
  showFilesOptionTicket,
  showRecorderTicket,
  hideMainLoader, //* (Temp : Solution) Prevent loader show when ticket chat and top header chat both are open (* Bypass Loader things)
  toggleFileOption,
  handleAttachmentClose,
  handleToggleRecorder,
  toggleEmojiPicker,
  showEmojiPicker,
}) => {
  const {
    replyingTo,
    setReplyingTo,
    selectedFiles,
    setChatAttachments,
    handleFileChange,
    handleAudioRecording,
    handleFileUpload,
    addNewMessage,
    handleReply,
    handleDelete,
    handleCopy,
    removeFile,
    updateFile,
    loadingFiles,
    setLoadingFiles,
    handleEdit,
    editValue,
    addNewThreadMessage,
  } = useConversation(activeRoom);
  const dispatch = useDispatch();
  const { loader, hasMoreHistory, hasMorThradeHistory } = useAppSelector(
    (state) => state.socketConfig
  );
  const {
    getChatRoomMessagesHistory,
    getChatThreadRoomMessagesHistory,
    addReaction,
    handleReadMessage,
  } = useChat();

  const onHandleThread = (message: ChatHistoryItem) => {
    dispatch(setThreadinitiatMessage(message));
    handleThread(message);
  };

  const [getRoomDetails, { data, isLoading }] = useGetRoomDetailsMutation();

  const paginationRef = useRef({
    page: 0,
    size: 25,
  });

  useEffect(() => {
    getRoomDetails({
      roomId: activeRoom.roomId,
    });

    return () => {
      // if(openThread){
      onThreadClose();
      // }
    };
  }, []);

  const fetchOlderMessages = async () => {
    const payload = {
      roomId: activeRoom.roomId,
      size: paginationRef.current.size,
      page: paginationRef.current.page + 1,
      userToken: sessionStorage.getItem("userId") as string,
      deviceUniqueId: sessionStorage.getItem("deviceUniqueId") as string,
    };
    paginationRef.current = {
      size: paginationRef.current.size,
      page: paginationRef.current.page + 1,
    };

    dispatch(
      setLoader({
        fetchMoreHistory: true,
      })
    );
    getChatRoomMessagesHistory(payload);
  };

  const fetchOlderThreadMessages = async () => {
    const payload = {
      roomId: activeRoom.roomId,
      size: paginationRef.current.size,
      page: paginationRef.current.page + 1,
      userToken: sessionStorage.getItem("userId") as string,
      deviceUniqueId: sessionStorage.getItem("deviceUniqueId") as string,
      clientMessageId: threadInitiatMessage?.clientMessageId as string,
    };
    paginationRef.current = {
      size: paginationRef.current.size,
      page: paginationRef.current.page + 1,
    };

    dispatch(
      setLoader({
        fetchMoreThreadHistory: true,
      })
    );
    getChatThreadRoomMessagesHistory(payload);
  };

  const handleReaction = (type: ReactionType, message: ChatHistoryItem) => {
    const payload = {
      reaction: type,
      clientMessageId: message.clientMessageId,
      timestamp: message.timestamp,
      userToken: sessionStorage.getItem("userId") as string,
      roomId: activeRoom.roomId,
      deviceUniqueId: sessionStorage.getItem("deviceUniqueId") as string,
    };
    addReaction(payload);
  };

  const showInputBox = isLegalUser(activeRoom);
  
  const mentionList = useMemo(
    () =>
      data?.data?.memberDetailDTOList?.map((member) => ({
        value: member.displayName,
        id: member.userToken.toString(),
      })) || [],
    [data?.data?.memberDetailDTOList]
  );

  if (
    !hideMainLoader &&
    (loader?.history || isLoading || loader.threadHistory)
  ) {
    return <Loader />;
  }

  return (
    <div>
      <div className="k-chat k-chat-100 position-relative no-border">
        {openThread && (
          <>
            <div className="d-flex justify-content-between p-2 align-items-center">
              <h5>Thread</h5>
              <button
                className="k-button k-button-md k-button-flat k-button-flat-base k-rounded-md k-icon-button rx-atc-btn"
                onClick={() => {
                  dispatch(setThreadinitiatMessage(null));
                  onThreadClose();
                }}
              >
                <IoCloseOutline
                  style={{
                    height: 19,
                    width: 19,
                    color: "var(--message-text)",
                  }}
                />
              </button>
            </div>
          </>
        )}
        <div
          className="k-message-list k-avatars"
          role="log"
          aria-label="Message list"
          aria-live="polite"
        >
          {loader.fetchMoreHistory && (
            <div className="d-flex justify-content-center w-100">
              <div className="chat-loader">
                <ClipLoader size={30} className="spinner" />{" "}
              </div>
            </div>
          )}
          {openThread ? (
            <div
              id="scrollable-chat"
              style={{
                overflow: "auto",
                display: "flex",
                flexDirection: "column-reverse",
              }}
            >
              <div className="w-100 flex-grow-1 pe-2">
                <RxInfiniteScroll
                  dataLength={chatThreadHistory.length}
                  next={fetchOlderThreadMessages}
                  hasMore={hasMorThradeHistory}
                  loader={<></>}
                  scrollableTarget="scrollable-thread-chat"
                  inverse={true}
                >
                  <ConversationThreadHistory
                    handleReply={handleReply}
                    handleReaction={handleReaction}
                    chatHistory={chatThreadHistory}
                    hasMoreHistory={hasMoreHistory}
                    handleDelete={handleDelete}
                    handleCopy={handleCopy}
                    handleEdit={handleEdit}
                    activeRoom={activeRoom}
                    threadInitiatMessage={threadInitiatMessage}
                  />
                </RxInfiniteScroll>
              </div>
            </div>
          ) : (
            <div
              id="scrollable-chat"
              style={{
                overflow: "auto",
                display: "flex",
                flexDirection: "column-reverse",
              }}
            >
              <div
                className="w-100 flex-grow-1 pe-2"
                // style={{
                //   flexGrow: 1,
                // }}
              >
                <RxInfiniteScroll
                  dataLength={chatHistory.length}
                  next={fetchOlderMessages}
                  hasMore={hasMoreHistory}
                  loader={<></>}
                  scrollableTarget="scrollable-chat"
                  inverse={true}
                >
                  <ConversationHistory
                    handleReply={handleReply}
                    handleReaction={handleReaction}
                    chatHistory={chatHistory}
                    hasMoreHistory={hasMoreHistory}
                    handleDelete={handleDelete}
                    handleCopy={handleCopy}
                    handleEdit={handleEdit}
                    handleThread={onHandleThread}
                    activeRoom={activeRoom}
                  />
                </RxInfiniteScroll>
              </div>
            </div>
          )}
        </div>

        {showInputBox && (
          <>
            {replyingTo && (
              <ReplyToCard
                replyingTo={replyingTo}
                setReplyingTo={setReplyingTo}
              />
            )}

            {(selectedFiles.length > 0 || showRecorder) && (
              <FileList
                selectedFiles={selectedFiles}
                removeFile={removeFile}
                updateFile={updateFile}
                loadingFiles={loadingFiles}
                setLoadingFiles={setLoadingFiles}
                setChatAttachments={setChatAttachments}
                // handleAudioRecording={handleAudioRecording}
                handleFileUpload={handleFileUpload}
                // showRecorder={showRecorder}
              />
            )}

            <InputBox
              addNewMessage={
                threadInitiatMessage ? addNewThreadMessage : addNewMessage
              }
              handleFileChange={handleFileChange}
              toggleFileOption={toggleFileOption}
              handleAttachmentClose={handleAttachmentClose}
              toggleEmojiPicker={toggleEmojiPicker}
              showFilesOption={showFilesOption}
              showFilesOptionTicket={showFilesOptionTicket}
              showEmojiPicker={showEmojiPicker}
              handleToggleRecorder={handleToggleRecorder}
              loadingFiles={loadingFiles}
              mentionList={mentionList}
              value={editValue?.message || ""}
              activeRoom={activeRoom}
              showRecorder={showRecorder}
              showRecorderTicket={showRecorderTicket}
              onRecordingSave={(data) => handleAudioRecording(data.blob)}
            />
          </>
        )}
      </div>
    </div>
  );
};

export default Chats;
