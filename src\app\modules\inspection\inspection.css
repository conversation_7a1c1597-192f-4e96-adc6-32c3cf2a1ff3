.block-card {
  background-color: var(--Rx-15-F6-color) !important;
  border-radius: 10px;
  color: var(--Rx-title);
  padding: 20px;
  margin-bottom: 20px;
}

.question-container {
  margin-top: 5px;
  padding-top: 10px;
  position: relative;
}

.file-input {
  margin-top: 10px;
}

.attachment-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 5px;
}

.drag-icon {
  cursor: grab;
  height: 20px;
  width: 20px;
}

.delete-icon {
  height: 20px !important;
  width: 20px !important;
}

.question-no-wrapper,
.grad-gradingOptionPoint-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.inspection-page .btn.rx-btn {
  border: 1px solid var(--Rx-btn-bg) !important;
}

/* 

.inspection-page div[data-rbd-placeholder-context-id] {
  background-color: rgba(0, 255, 0, 0.2);
  border: 2px dashed #00ff00;
  border-radius: 4px;
  opacity: 0.5;
} */


.area-attachment-input-wrapper .add-attachment-button {
  position: absolute;
  top: 7px;
  left: 130px;
}

.question-attachment-input-wrapper .add-attachment-button {
  position: absolute;
  top: -8px;
  left: 90px;
}

.point-wrapper .select__option {
  align-items: center;
  display: flex;
}

.point-wrapper .select__option:before {
  border-radius: 10px;
  content: ' ';
  display: "block";
  margin-right: 8px;
  height: 10px;
  width: 10px;
}

.point-wrapper .select__option:nth-child(1)::before {
  background-color: #FF0000;
}
.point-wrapper .select__option:nth-child(2)::before {
  background-color: #FF4500;
}
.point-wrapper .select__option:nth-child(3)::before {
  background-color: #FFA500;
}
.point-wrapper .select__option:nth-child(4)::before {
  background-color: #FFFF00;
}
.point-wrapper .select__option:nth-child(5)::before {
  background-color: #ADFF2F;
}
.point-wrapper .select__option:nth-child(6)::before {
  background-color: #7FFF00;
}
.point-wrapper .select__option:nth-child(7)::before {
  background-color: #90EE90;
}
.point-wrapper .select__option:nth-child(8)::before {
  background-color: #32CD32;
}
.point-wrapper .select__option:nth-child(9)::before {
  background-color: #006400;
}
.point-wrapper .select__option:nth-child(10)::before {
  background-color: #00FF00;
}
.point-wrapper .select__option:nth-child(11)::before {
  background-color: #00FF00;
}
.point-wrapper .select__option:nth-child(12)::before {
  background-color: #8C8C8C;
}


.point-wrapper {
  min-width: 120px;
}