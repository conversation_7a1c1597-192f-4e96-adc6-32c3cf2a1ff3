import { Card } from "react-bootstrap";
import { Attachment, QuestionResponse } from "../../../apis/type";
import { ImAttachment } from "react-icons/im";
import { CgNotes } from "react-icons/cg";
import { useState } from "react";
import MediaPreviewModal from "../../common/MediaPreviewModal";
import { getPointOptionByValue } from "../../../utils/helper";
import { Tooltip } from "@progress/kendo-react-tooltip";
import { BsTicket } from "react-icons/bs";
import { useNavigate } from "react-router";
import ImageGrid from "./ImageGrid";

const QuestionCard = ({
  question,
  questionNumber,
}: {
  question: QuestionResponse;
  questionNumber: number;
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [mediaItems, setMediaItems] = useState<any[]>([]);
  const [initialAnsAttachmentIndex, setInitialAnsAttachmentIndex] = useState<
    string | null
  >(null);
  const navigate = useNavigate();

  const handleAttachmentClick = (images: Attachment[]) => {
    const attachments = images?.map((attachment) => ({
      id: attachment.attachmentId,
      type: attachment.attachmentType.toLowerCase(),
      src: attachment.attachmentUrl,
    }));
    setMediaItems(attachments || []);
    setIsModalOpen(true);
  };

  const handleTicketClick = (ticketId: string) => {
    navigate("/tickets/ticketdetail", {
      state: {
        ticketid: ticketId,
      },
    });
  };

  return (
    <>
      <Card className="border-0 rounded-3">
        <Card.Body className="p-3">
          <div>
            <div
              className="d-flex align-items-center gap-2"
              style={{
                minWidth: 120,
                justifyContent: "flex-end",
              }}
              id="hide-in-print"
            >
              <div style={{ width: 30 }}>
                {question?.ticketId && (
                  <Tooltip
                    parentTitle={true}
                    anchorElement="target"
                    position="bottom"
                    style={{
                      maxWidth: 400,
                    }}
                  >
                    <span
                      className="cursor-pointer"
                      title={"View Ticket"}
                      id="hide-in-print"
                      role="button"
                      onClick={() => handleTicketClick(question?.ticketId)}
                    >
                      <BsTicket size={20} />
                    </span>
                  </Tooltip>
                )}
              </div>
              <div
                className={"rounded-circle"}
                style={{
                  width: 10,
                  height: 10,
                  background: getPointOptionByValue(question.points)?.color,
                }}
              ></div>
              <span>{question.answerText}</span>
            </div>
            <div
              className="d-flex flex-wrap flex-md-nowrap align-items-center gap-3"
              style={{ marginTop: -10, width: "calc(100% - 140px)" }}
            >
              <div
                className="d-flex justify-content-center align-items-center rounded-circle bg-white text-dark"
                style={{ width: 32, height: 32 }}
              >
                {questionNumber}
              </div>
              {question?.inspectionTemplateQuestionAttachment?.[0]
                ?.attachmentUrl && (
                <div
                  className="rounded-circle overflow-hidden position-relative"
                  style={{ width: 32, height: 32, background: "var(-Black-3)" }}
                  id="hide-in-print"
                >
                  <img
                    src={
                      question?.inspectionTemplateQuestionAttachment?.[0]
                        ?.attachmentUrl || ""
                    }
                    alt=""
                    onClick={() =>
                      handleAttachmentClick(
                        question?.inspectionTemplateQuestionAttachment
                      )
                    }
                    style={{
                      cursor: "pointer",
                    }}
                  />
                  {question?.inspectionTemplateQuestionAttachment?.length >
                    1 && (
                    <div
                      className="position-absolute top-0 start-0 w-100 h-100 d-flex justify-content-center align-items-center"
                      style={{
                        background: "rgba(0, 0, 0, 0.7)",
                        color: "white",
                        fontSize: "12px",
                        cursor: "pointer",
                        pointerEvents: "none",
                      }}
                    >
                      +
                      {question?.inspectionTemplateQuestionAttachment?.length -
                        1}
                    </div>
                  )}
                </div>
              )}
              <div className="flex-grow-1 small">{question.questionText}</div>
            </div>
            <div style={{ marginLeft: "50px" }} id="hide-in-print">
              <div className="d-flex align-items-center gap-4 ms-auto justify-content-between">
                <div>
                  {question?.description && (
                    <>
                      <h6 className="mt-2">Comment</h6>
                      <p
                        className="cursor-pointer"
                        title={question.description}
                      >
                        {question.description}
                      </p>
                    </>
                  )}
                </div>
                <div style={{ marginTop: -20 }}>
                  <ImageGrid
                    images={question?.inspectionTemplateAnswerAttachment || []}
                    onClick={(img) => {
                      setInitialAnsAttachmentIndex(img?.attachmentId);
                      handleAttachmentClick(
                        question?.inspectionTemplateAnswerAttachment
                      );
                    }}
                  />
                </div>
              </div>
            </div>
          </div>
        </Card.Body>
      </Card>

      {isModalOpen && (
        <MediaPreviewModal
          mediaItems={mediaItems}
          onClose={() => setIsModalOpen(false)}
          initialIndex={initialAnsAttachmentIndex?.toString()}
        />
      )}
    </>
  );
};

export default QuestionCard;
