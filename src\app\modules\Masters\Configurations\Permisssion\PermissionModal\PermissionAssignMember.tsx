import { useEffect, useState } from 'react';
import { Col, Modal, Row } from 'react-bootstrap'
import { RxCross2 } from 'react-icons/rx'
import { escalationService } from '../../Escalation/escalation.helper';
import SwalMessage from '../../../../common/SwalMessage';
import encryptDecryptUtil from '../../../../../utils/encrypt-decrypt-util';
import { getImage } from '../../../../../utils/CommonUtils';
import userSVG from "../../../../../../_metronic/assets/SideMenuIcon/MenuallSVGIcon/user.svg";
import { permissionService } from '../Permission.helper';

const PermissionAssignMember = ({ addPermissionUserModal, setAddpermissionUserModal, userData, setUserData, permissionId, setPermissionId, getPermissionGridData, setLoading }: any) => {
    const [SelectMemberData, setSelectMemberData] = useState<any[]>([]);
    const [search, setSearch] = useState<string>('');
    const [isSearch, setIsSeach] = useState<boolean>(false);
    const [error, setError] = useState<string>('');
    const [isdisabled, setIsDisable] = useState<any>(false);
    const [displayUserData, setDisplayUserData] = useState<any[]>([]);
    // Internal getUserMemberData function for search within modal
    const getUserMemberDataInternal = async (search: string) => {
        try {
            let keyinfo = JSON.parse(localStorage.keyinfo);
            setLoading(true);
            await permissionService.getmembersforpermission(permissionId, 1, search)
                .then((res: any) => {
                    if (res?.data?.success) {
                        const result = encryptDecryptUtil.decryptData(
                            res?.data?.data,
                            keyinfo.syckey
                        );
                        const encResponse = JSON.parse(result);
                        const sortedData = (encResponse || []).sort((a: any, b: any) => {
                            const aSelected = a.isSelect === 1 ? 1 : 0;
                            const bSelected = b.isSelect === 1 ? 1 : 0;
                            return bSelected - aSelected;
                        });
                        setDisplayUserData(sortedData || []);
                    }
                }).catch((err: any) => {
                    SwalMessage('error', err?.message, 'OK', 'error', false);
                }).finally(() => {
                    setLoading(false);
                });
        } catch (error: any) {
            SwalMessage('error', error.message, 'OK', 'error', false);
            setLoading(false);
        } finally {
            setLoading(false)
        }
    };

    useEffect(() => {
        if (addPermissionUserModal && SelectMemberData.length > 0 && displayUserData.length > 0) {
            setIsDisable(true)
        } else {
            setIsDisable(false)
        }
    }, [SelectMemberData, displayUserData])

    // Store original data when modal opens and userData changes from parent
    useEffect(() => {
        if (addPermissionUserModal) {
            const sortedData = (userData || []).sort((a: any, b: any) => {
                const aSelected = a.isSelect === 1 ? 1 : 0;
                const bSelected = b.isSelect === 1 ? 1 : 0;
                return bSelected - aSelected;
            });
            setDisplayUserData(sortedData);
            const preSelected = userData?.filter((item: any) => item.isSelect === 1);
            setSelectMemberData(preSelected || []);
            // setSelectMemberData(selectedmemberData); // pre-fill selection from parent
        }
    }, [addPermissionUserModal]);

    // Handle search with debounce using internal function
    useEffect(() => {
        if (addPermissionUserModal && search?.length >= 0 && isSearch) {
            const handler = setTimeout(() => {
                getUserMemberDataInternal(search);
            }, 400);
            return () => {
                clearTimeout(handler);
            };
        }
    }, [search, addPermissionUserModal]);

    const value =
        SelectMemberData === null || undefined
            ? []
            : SelectMemberData?.map((data: any) => data?.value);

    const handleSelectMember = (member: any) => {
        const isAlreadySelected = SelectMemberData.some((m: any) => m.value === member.value);

        if (isAlreadySelected) {
            const updatedSelection = SelectMemberData.filter(
                (m: any) => m.value !== member.value
            );
            setSelectMemberData(updatedSelection);
            setError('');
        } else {
            setSelectMemberData((prevSelectedMembers: any) => [
                ...prevSelectedMembers,
                member
            ]);
            setError('');
        }
    };

    const savedata = async () => {
        if (SelectMemberData.length === 0) {
            setError('Please select at least one member.');
            return;
        }
        const selectedmemberData = SelectMemberData.map((item: any) => item?.value)
        await permissionService.saveMemberForPermission(permissionId, selectedmemberData)
            .then((res: any) => {
                if (res?.data?.success) {
                    SwalMessage('success', res?.data.errormsg, 'OK', 'success', false)
                        .then((isConfirm) => {
                            if (isConfirm) {
                                setAddpermissionUserModal(false);
                                getPermissionGridData(1)
                            }
                        })
                } else {
                    SwalMessage('error', res?.data?.errormsg, 'OK', 'error', false);
                }
            }).catch((err: any) => {
                SwalMessage('error', err?.message, 'OK', 'error', false);
            }).finally(() => {
                setLoading(false);
            })
    }

    // Clear search function


    // Clean up when modal closes - no API calls
    useEffect(() => {
        if (!addPermissionUserModal) {
            setSelectMemberData([]);
            setSearch('');
            setError('');
            setIsDisable(false);
            setDisplayUserData([]);
            setIsSeach(false);
            setUserData([]);
            setPermissionId('');
        }
    }, [addPermissionUserModal]);

    return (
        <>
            <Modal show={addPermissionUserModal} onHide={() => setAddpermissionUserModal(false)} className='modal-right p-0' scrollable={true}>
                <Modal.Header className="border-0 mb-3">
                    <Row className="align-items-baseline">
                        <Col xs={10} className="mt-auto mb-auto">
                            <h2 className="mb-0">Assign Permission to Users</h2>
                        </Col>
                        <Col xs={2} className="text-end mb-3">
                            <span
                                className="close-btn cursor-pointer"
                                onClick={() => setAddpermissionUserModal(false)}
                            >
                                <RxCross2 fontSize={20} />
                            </span>
                        </Col>
                        <Col sm={12} className="mt-3 position-relative">
                            <input
                                type="text"
                                className="form-control"
                                placeholder="Search"
                                value={search}
                                onChange={(e) => { setIsSeach(true), setSearch(e.target.value) }}
                            />
                        </Col>
                        <Col sm={12} className="mt-3 text-end">
                            <span className="text-danger">{error}</span>
                        </Col>
                    </Row>
                </Modal.Header>
                <Modal.Body className='pt-0'>
                    <div>
                        {displayUserData?.map((item: any) => (
                            <div
                                className="d-flex align-items-center mt-6 pb-5 border-bottom"
                                key={item.value}
                            >
                                <input
                                    type="checkbox"
                                    checked={SelectMemberData?.map(
                                        (data: any) => data?.value || data?.id
                                    ).includes(item?.value)}
                                    className="text-white"
                                    onChange={() => handleSelectMember(item)}
                                    name={item?.value}
                                    id={`savedata-checkbox-${item?.value}`}
                                />
                                <label
                                    htmlFor={`savedata-checkbox-${item?.value}`}
                                    className="user-select-none d-flex align-items-center ms-3 cursor-pointer"
                                >
                                    <img
                                        src={item?.image ? getImage(item?.image) : userSVG}
                                        className="rounded-circle"
                                        height={"32px"}
                                        width={"32px"}
                                    />
                                    {item?.isExternal === 1 ? (
                                        <span className="ms-2">
                                            {item?.label}{" "}
                                            <i className="d-block text-gray-800 fs-12px">
                                                {item?.companyname}
                                            </i>
                                        </span>
                                    ) : (
                                        <span className="ms-2">{item?.label}</span>
                                    )}
                                </label>
                            </div>
                        ))}
                    </div>
                </Modal.Body>
                <Modal.Footer className="border-0 p-0 ">
                    <button className={`btn ${isdisabled ? "btn-success" : "rx-btn"}`}
                        onClick={() => savedata()}>
                        Save & Continue
                    </button>
                </Modal.Footer>
            </Modal>
        </>
    )
}

export default PermissionAssignMember