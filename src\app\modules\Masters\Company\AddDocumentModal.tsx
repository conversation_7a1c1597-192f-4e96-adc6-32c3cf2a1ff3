import { ChangeEvent, useEffect, useState } from "react";
import { Card, Col, Form, Modal, Row } from "react-bootstrap";
import { BsSave } from "react-icons/bs";
import { SlClose, SlCloudUpload, SlEye, SlTrash } from "react-icons/sl";
import { Link, useNavigate } from "react-router-dom";
import Preview_File_Modal from "../../pages/Preview_File_Modal";
import { extensionPacks, getImage } from "../../../utils/CommonUtils";
import SwalMessage from "../../common/SwalMessage";
import noImage from "../../../../efive_assets/images/nofile.jpg";
import fileimage from "../../../../efive_assets/images/file.png";
import videoImage from "../../../../efive_assets/images/video.png";
import audioImage from "../../../../efive_assets/images/audio-img.png";
import { companyService } from "./company.helper";
import encryptDecryptUtil from "../../../utils/encrypt-decrypt-util";

const AddDocumentModal = ({
  documentModal,
  setDocumentModal,
  setGridLoading,
  setdocumentsList,
  documentsList,
  isEditDocumnet,
  singleDocument,
  setisEditDocumnet,
  companyid,
}: any) => {
  const navigate = useNavigate();
  // const [loading, setLoading] = useState(false);
  const [docTitle, setDocTitle] = useState<any>();
  const [docDescription, setDocDescription] = useState<any>(null);
  const [isDocTitleValid, setIsDocTitleValid] = useState<any>(true);
  const [docTitleMsg, setDocTitleMsg] = useState<any>(null);
  const [docPayload, setDocPayload] = useState<any>(null);
  const [isDocPayloadValid, setIsDocPayloadValid] = useState(true);
  const [docPayloadMsg, setDocPayloadMsg] = useState<any>(null);
  const [viewDoc, setViewDoc] = useState<string>("");
  const [previewfilemodal, setpreviewfilemodal] = useState(false);
  const fileSize = JSON.parse(localStorage.getItem("userinfo") as string);
  useEffect(() => {
    if (singleDocument) {
      setDocDescription(singleDocument?.description);
      setDocTitle(singleDocument?.title);
      setDocPayload(singleDocument?.documenturl);
      setViewDoc(getImage(singleDocument?.documenturl));
    } else {
      setDocTitle("");
      setDocTitleMsg("");
      setDocPayload("");
      setDocPayloadMsg("");
      setDocDescription("");
      setViewDoc("");
    }
  }, [documentModal]);

  const handleImageChange = (event: ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = event.target.files;
    if (selectedFiles && selectedFiles.length > 0) {
      const file = selectedFiles[0];
      if (extensionPacks.documentExtensions.includes(file.type)) {
        if (file.size <= fileSize.uploaddocumentsize * 1024 * 1024) {
          setDocPayload(file);
          setViewDoc(URL.createObjectURL(file));
        } else {
          SwalMessage(
            null,
            `Please upload .pdf under ${fileSize.uploaddocumentsize}MB size`,
            "Ok",
            "error",
            false
          );
          return null;
        }
      } else {
        SwalMessage(
          null,
          "Please choose files with the following extensions: .pdf",
          "Ok",
          "error",
          false
        );
      }
    }

    event.target.value = "";
  };
  const handleDeleteImage = () => {
    setDocPayload(null);
    setViewDoc("");
  };

  const validatePayload = () => {
    if (!docPayload) {
      setIsDocPayloadValid(false);
      setDocPayloadMsg("Please Select File");
      return;
    } else {
      setIsDocPayloadValid(true);
      setDocPayloadMsg("");
    }
    if (docTitle && docTitle.trim()) {
      setIsDocTitleValid(true);
      setDocTitleMsg(null);
    } else {
      setIsDocTitleValid(false);
      setDocTitleMsg("Please Enter Document Title");
      return;
    }
    return true;
  };

  const saveDocument = () => {
    if (validatePayload()) {
      setGridLoading(true);
      let payload = {
        documentid: singleDocument?.documentid
          ? singleDocument?.documentid
          : "",
        title: docTitle,
        description: docDescription,
        companyid: companyid,
      };

      const documentPayload = singleDocument?.documentid
        ? docPayload.size
          ? docPayload
          : ""
        : docPayload;
      companyService
        .uploadDocument(payload, documentPayload)
        .then((response: any) => {
          if (response.status == 200) {
            if (response.data?.success === true) {
              SwalMessage(
                null,
                response.data?.errormsg,
                "Ok",
                "success",
                false
              );
              let keyinfo = JSON.parse(localStorage.keyinfo);
              const result = encryptDecryptUtil.decryptData(
                response.data?.data,
                keyinfo?.syckey
              );
              const parseData = JSON.parse(result);

              setDocTitle(null);
              setDocTitleMsg(null);
              setDocPayload(null);
              setDocPayloadMsg(null);
              setDocDescription(null);
              setViewDoc("");
              if (!singleDocument?.documentid) {
                setdocumentsList([
                  ...documentsList,
                  {
                    documentStatus: parseData?.documentStatus,
                    documentid: parseData?.documentid,
                    documenturl: parseData?.documenturl,
                    title: docTitle,
                    description: docDescription,
                  },
                ]);
              } else {
                setdocumentsList(
                  documentsList.map((item: any) => {
                    if (item.documentid == singleDocument?.documentid) {
                      return {
                        ...item,
                        title: docTitle,
                        description: docDescription,
                      };
                    }
                    return item;
                  })
                );
              }
              setGridLoading(false);
              setisEditDocumnet(null);
              setDocumentModal(false);
            } else {
              setGridLoading(false);
              SwalMessage(null, response.data?.errormsg, "Ok", "error", false);
            }
          }
        })
        .catch((error: any) => {
          if (error.response?.status == 401) {
            // localStorage.removeItem("islogin");
            navigate("/admindashboard");
            // navigate(0);
          } else if (error?.code === "ERR_NETWORK") {
            setGridLoading(false);
            SwalMessage(null, error?.message, "Ok", "error", false);
          }
          setGridLoading(false);
          SwalMessage(null, error?.message, "Ok", "error", false);
        })
        .finally(() => {
          setGridLoading(false);
        });
    }
  };

  const handleview = async () => {
    function blobToURL(blob: any) {
      return new Promise((resolve) => {
        const reader = new FileReader();
        reader.readAsDataURL(blob);
        reader.onloadend = function () {
          const base64data = reader.result;
          resolve(base64data);
        };
      });
    }
    const arrayBuffer = await fetch(viewDoc);
    const blob = await arrayBuffer.blob();
    const URL = await blobToURL(blob);
    const fileName = viewDoc.split(`attachment_pdf_`).pop() || "default.pdf";
    const anchor = document.createElement("a");
    anchor.href = URL as string;
    anchor.download = fileName;
    document.body.appendChild(anchor);
    anchor.click();
    document.body.removeChild(anchor);
  };
  // console.log("documentsList", documentsList, isEditDocumnet, singleDocument)
  return (
    <>
      <Modal className="modal-right" scrollable={true} show={documentModal}>
        <Modal.Header className="p-0">
          <Row>
            <Col sm={5} className="mt-auto mb-auto mobile-margin">
              <h2 className="mb-0">UPLOAD DOCUMENTS</h2>
            </Col>
            <Col sm={7} className=" text-end mb-3">
              {(isEditDocumnet || isEditDocumnet == null) && (
                <a
                  className="btn rx-btn me-6"
                  onClick={saveDocument}
                >
                  Save
                </a>
              )}

              <Link
                to={""}
                className="btn rx-btn "
                onClick={() => setDocumentModal(false)}
              >
                <SlClose className="btn-icon-custom" />
                Close
              </Link>
            </Col>
          </Row>
        </Modal.Header>
        <Modal.Body className="p-0">
          <Card className="mt-5 h-100">
            <Card.Body>
              <Row className="mt-5">
                <Col xxl={12} xl={12} lg={12} sm={12}>
                  <Row>
                    <Col xxl={12} xl={12} lg={12} sm={12}>
                      {(isEditDocumnet || isEditDocumnet == null) && (
                        <>
                          <Form.Label>
                            Document
                            <span className="text-danger ms-3">
                              (Max {`${fileSize.uploaddocumentsize}`}MB
                              Document)
                            </span>
                          </Form.Label>
                          <div className="d-flex align-items-center gap-2">
                            <label htmlFor="file-upload-company">
                              <span className="btn rx-btn cursor-pointer">
                                <SlCloudUpload className="btn-icon-custom" />
                                Upload
                                <input
                                  id="file-upload-company"
                                  type="file"
                                  accept="application/pdf"
                                  onChange={(e: any) => handleImageChange(e)}
                                  style={{ display: "none" }}
                                />
                              </span>
                            </label>
                          </div>
                        </>
                      )}

                      <div className="mt-4">
                        <Row>
                          <Col
                            xxl={4}
                            xl={4}
                            lg={4}
                            sm={6}
                            className="mb-5 text-center"
                          >
                            <img
                              src={docPayload ? fileimage : noImage}
                              className="image-input-wrapper"
                            />

                            {viewDoc && (
                              <div className="text-center mt-2">
                                {!singleDocument ? (
                                  <a
                                    className="btn btn-dark image-action-btn me-2"
                                    href={viewDoc ? viewDoc : "#"}
                                    target="_blank"
                                  >
                                    <SlEye className="img-action-icon" />
                                  </a>
                                ) : (
                                  <span className="btn btn-dark image-action-btn me-2" onClick={handleview}>
                                    <SlEye className="img-action-icon" />
                                  </span>
                                )}

                                {(isEditDocumnet || isEditDocumnet == null) && (
                                  <span className="btn btn-dark image-action-btn ">
                                    <SlTrash
                                      className=" img-action-icon"
                                      onClick={handleDeleteImage}
                                    />
                                  </span>
                                )}
                              </div>
                            )}
                          </Col>
                          {!isDocPayloadValid ? (
                            <>
                              <span className="text-danger">
                                {docPayloadMsg}
                              </span>
                            </>
                          ) : (
                            ""
                          )}
                        </Row>
                      </div>
                    </Col>
                    <Col xxl={12} xl={12} lg={12} sm={12} className="mt-5">
                      <Form.Label>
                        Title<span className="text-danger">*</span>
                      </Form.Label>
                      <Form.Control
                        placeholder="Enter Title"
                        maxLength={64}
                        disabled={isEditDocumnet === false}
                        value={docTitle}
                        onChange={(e) => setDocTitle(e.target.value)}
                      ></Form.Control>
                      {!isDocTitleValid ? (
                        <>
                          <span className="text-danger">{docTitleMsg}</span>
                        </>
                      ) : (
                        ""
                      )}
                    </Col>
                    <Col xxl={12} xl={12} lg={12} sm={12} className="mt-5">
                      <Form.Label>Description</Form.Label>
                      <textarea
                        className="form-control"
                        placeholder="Enter Description"
                        disabled={isEditDocumnet === false}
                        maxLength={512}
                        value={docDescription}
                        onChange={(e) => setDocDescription(e.target.value)}
                      ></textarea>
                    </Col>
                  </Row>
                </Col>
              </Row>
            </Card.Body>
          </Card>
        </Modal.Body>
      </Modal>
      <Preview_File_Modal
        setpreviewfilemodal={setpreviewfilemodal}
        previewfilemodal={previewfilemodal}
        previewFile={viewDoc}
      />
    </>
  );
};

export default AddDocumentModal;
