import React from "react";
import { Image } from "react-bootstrap";
import { FaFilePdf, FaFileWord, FaFileAlt, FaFileAudio } from "react-icons/fa";

interface Attachment {
  attachmentId: string;
  attachmentType: string;
  attachmentUrl: string;
}

interface ImageGridProps {
  images: Attachment[];
  onClick?: (images: Attachment) => void;
}

const ImageGrid: React.FC<ImageGridProps> = ({ images, onClick }) => {
  const maxVisible = 4;
  const visibleImages = images.slice(0, maxVisible);
  const remaining = images.length - maxVisible;
  console.log('visibleImages', visibleImages)
  return (
    <div
      className="d-flex flex-wrap justify-content-end"
      style={{ width: "115px", gap: 5 }}
    >
      {visibleImages.map((img, index) => {
        let content;
        if (img.attachmentType?.toLowerCase() === "image") {
          content = (
            <Image
              src={img.attachmentUrl}
              alt={`img-${index}`}
              style={{ width: "100%", height: "100%", objectFit: "cover" }}
              rounded
            />
          );
        } else if (img.attachmentType?.toLowerCase() === "video") {
          content = (
            <video
              src={img.attachmentUrl}
              style={{
                width: "100%",
                height: "100%",
                objectFit: "cover",
              }}
              className="rounded"
              controls={false}
              muted
            />
          );
        } else if (img.attachmentType?.toLowerCase() === "audio") {
          content = (
            <div className="d-flex align-items-center justify-content-center w-100 h-100 bg-light rounded">
              <FaFileAudio size={24} color="#888" />
            </div>
          );
        }
        //  else if (img.attachmentType === "PDF") {
        //   content = (
        //     <div className="d-flex align-items-center justify-content-center w-100 h-100 bg-light rounded">
        //       <FaFilePdf size={24} color="#d32f2f" />
        //     </div>
        //   );
        // } else if (img.attachmentType === "DOC") {
        //   content = (
        //     <div className="d-flex align-items-center justify-content-center w-100 h-100 bg-light rounded">
        //       <FaFileWord size={24} color="#1976d2" />
        //     </div>
        //   );
        // }
        else {
          content = (
            <div className="d-flex align-items-center justify-content-center w-100 h-100 bg-light rounded">
              <FaFileAlt size={24} color="#888" />
            </div>
          );
        }
        return (
          <div
            key={img.attachmentId || index}
            className="position-relative m-0 p-0 cursor-pointer"
            style={{ width: "50px", height: "40px", overflow: "hidden" }}
            onClick={() => onClick?.(img)}
          >
            {content}
            {index === 3 && remaining > 0 && (
              <div
                className="position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center"
                style={{
                  backgroundColor: "rgba(0, 0, 0, 0.5)",
                  color: "white",
                  fontWeight: "bold",
                  fontSize: "16px",
                  borderRadius: "0.25rem",
                }}
              >
                +{remaining}
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
};

export default ImageGrid;
