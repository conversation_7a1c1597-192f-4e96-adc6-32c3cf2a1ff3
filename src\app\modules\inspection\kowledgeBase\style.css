/* .know-base-section .image_video_thumb {
  width: 50px;
  height: 50px;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid #ddd;
} */

.know-base-section .document-thumb,
.know-base-section .image_video_thumb {
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.know-base-section .image_video_thumb img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4px;
}

.title-div {
  font-weight: bold;
  font-size: 16px;
}

.des-div {
  color: #6c757d;
  font-size: 14px;
}

.img-video-div {
  border-bottom: 1px solid #eee;
}

.img-video-div:last-child {
  border-bottom: none;
}

/* .image-action-btn {
  width: 36px;
  height: 36px;
  padding: 6px;
  border-radius: 4px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: #343a40 !important;
  border-color: #343a40 !important;
  transition: all 0.2s ease;
}

.image-action-btn:hover {
  background-color: #23272b !important;
  border-color: #1d2124 !important;
  transform: translateY(-2px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.img-action-icon {
  font-size: 16px;
  color: #fff !important;
} */

.media-content-container {
  width: 100%;
  overflow-x: hidden;
}

/* Make text truncate work better for long descriptions */
.title-div.text-truncate,
.des-div.text-truncate {
  max-width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
