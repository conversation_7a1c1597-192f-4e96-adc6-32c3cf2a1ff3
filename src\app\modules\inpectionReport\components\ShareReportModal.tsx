import React, { useState, useRef, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, Form, Spinner, Row, Col } from "react-bootstrap";
import { InspectionReportDetails } from "../../../apis/type";
import RxReactSelect from "../../../component/RxReactSelect";
import { useSendMailReportMutation } from "../../../apis/inspectionEmailAPI";
import { RxCross2 } from "react-icons/rx";
import { LuEye } from "react-icons/lu";
import SwalMessage from "../../common/SwalMessage";
import { Loader } from "../../../component";
import PrintableReport from "./PrintableReport";
import jsPDF from "jspdf";
import html2canvas from "html2canvas";
import { formatCustomDate } from "../../../utils/helper";
import { convertPdfBlobToMultipleFormats } from "../../../utils/blobToFileUtils";

interface ShareReportModalProps {
  show: boolean;
  onHide: () => void;
  report: InspectionReportDetails;
}

interface EmailOption {
  value: string;
  label: string;
}

const ShareReportModal: React.FC<ShareReportModalProps> = ({
  show,
  onHide,
  report,
}) => {
  const [selectedEmails, setSelectedEmails] = useState<EmailOption[]>([]);
  const [subject, setSubject] = useState(
    `Report for Inspection: ${report?.inspectionTemplateTitle || ""}`
  );
  const [isLoading, setIsLoading] = useState(false);
  const printableContentRef = useRef<HTMLDivElement>(null);

  const [message, setMessage] = useState("");
  const [sendMailReport] = useSendMailReportMutation();
  const [showPreview, setShowPreview] = useState(false);
  const [isGeneratingPreview, setIsGeneratingPreview] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const emailOptions: EmailOption[] =
    report?.emailList?.map((item) => ({
      value: item,
      label: item,
    })) || [];

  // Function to handle PDF preview

  const validateForm = (): boolean => {
    if (selectedEmails.length === 0) {
      SwalMessage(
        null,
        "Please select at least one recipient",
        "Ok",
        "error",
        false
      );
      return false;
    }

    if (!subject.trim()) {
      SwalMessage(null, "Please enter a subject", "Ok", "error", false);
      return false;
    }

    return true;
  };

  const handleSend = async () => {
    if (!validateForm()) return;
    setIsLoading(true);
    const pdfBlob = await generatePDF();

    // Convert blob to multiple file formats
    if (pdfBlob) {
      const propertyName =
        report?.propertyDetailsResponse?.propertyName || "Property";
      const reportDate = report?.createdAt
        ? new Date(report.createdAt)
        : new Date();

      // Get all conversion formats
      const convertedFormats = await convertPdfBlobToMultipleFormats(
        pdfBlob,
        propertyName,
        reportDate
      );

      console.devAPILog("=== PDF BLOB CONVERTED TO MULTIPLE FORMATS ===");
      console.devAPILog("1. File Object:", convertedFormats.file);
      console.devAPILog("   - Name:", convertedFormats.fileName);
      console.devAPILog("   - Size:", convertedFormats.fileSize,);
      console.devAPILog("   - Type:", convertedFormats.fileType);

      console.devAPILog(
        "2. Base64 String (first 100 chars):",
        convertedFormats.base64.substring(0, 100) + "..."
      );

      console.devAPILog("3. ArrayBuffer:", convertedFormats.arrayBuffer);
      console.devAPILog("   - Byte Length:", convertedFormats.arrayBuffer.byteLength);

      console.devAPILog("4. Download URL:", convertedFormats.downloadUrl);

      console.devAPILog("5. FormData:", convertedFormats.formData);
      console.devAPILog("6. fileSizeInMB:", convertedFormats.fileSizeInMB);

      // Example: Download the file directly

      // Example: Use the File object for upload

      // Example: Use FormData for API call

      // Clean up URL when done
      // convertedFormats.revoke();

      try {
        // Create payload for the email
        const emailPayload = {
          to: selectedEmails.map((email) => email.value).join(","),
          subject: subject,
          contentType: "application/pdf",
          fileName: subject,
          files: pdfBlob ? pdfBlob : null,
          // files: convertedFormats ? [convertedFormats.file] : null,
        };

        // Send the email
        const response = await sendMailReport(emailPayload).unwrap();

        if (response.success) {
          SwalMessage(
            null,
            "Report shared successfully",
            "Ok",
            "success",
            false
          );
          onHide();
        } else {
          SwalMessage(
            null,
            response.errormsg || "Failed to share report",
            "Ok",
            "error",
            false
          );
        }
      } catch (error) {
        console.error("Error sharing report:", error);
        SwalMessage(
          null,
          "An error occurred while sharing the report",
          "Ok",
          "error",
          false
        );
      } finally {
        setIsLoading(false);
      }
    }
  };

  // Preview method 1: Opens PDF in new browser tab (current implementation)
  const handlePreview = async () => {
    setIsGeneratingPreview(true);
    try {
      const pdfBlob = await generatePDF();
      if (pdfBlob) {
        // Create a URL for the blob and open it in a new tab
        const pdfUrl = URL.createObjectURL(pdfBlob);
        window.open(pdfUrl, "_blank");

        // Clean up the URL after a delay to allow the browser to load it
        setTimeout(() => {
          URL.revokeObjectURL(pdfUrl);
        }, 1000);
      } else {
        SwalMessage(
          null,
          "Failed to generate PDF preview",
          "Ok",
          "error",
          false
        );
      }
    } catch (error) {
      console.error("Error generating preview:", error);
      SwalMessage(
        null,
        "An error occurred while generating preview",
        "Ok",
        "error",
        false
      );
    } finally {
      setIsGeneratingPreview(false);
    }
  };

  const generatePDF = async (): Promise<Blob | null> => {
    console.devAPILog("Starting PDF generation...");
    try {
      // Make the hidden div visible temporarily for rendering
      if (!printableContentRef.current) {
        console.error("Printable content ref not found");
        return null;
      }

      // Make the content visible but off-screen for better rendering
      printableContentRef.current.style.display = "block";
      printableContentRef.current.style.position = "absolute";
      printableContentRef.current.style.left = "-9999px";
      printableContentRef.current.style.top = "0";
      printableContentRef.current.style.width = "1024px"; // Set a fixed width for better rendering
      printableContentRef.current.style.height = "auto";
      printableContentRef.current.style.overflow = "visible";

      // Force layout calculation
      printableContentRef.current.getBoundingClientRect();

      // Wait for any images to load and for layout to stabilize
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // Create PDF document
      const pdf = new jsPDF("portrait", "pt", "a4");
      const pdfWidth = pdf.internal.pageSize.getWidth();

      // Find the main sections to capture separately
      const coverPage =
        printableContentRef.current.querySelector(".print-cover-page");
      const questionsSection = printableContentRef.current.querySelector(
        ".flex-grow-1.overflow-auto"
      );
      const inspectorSection = printableContentRef.current.querySelector(
        ".print-cover-page:last-child"
      );

      if (!coverPage || !questionsSection || !inspectorSection) {
        console.error("Could not find all required sections");
        printableContentRef.current.style.display = "none";
        return null;
      }

      // Capture cover page
      console.devAPILog("Capturing cover page...");
      const coverCanvas = await html2canvas(coverPage as HTMLElement, {
        scale: 0.8, // Lower scale for smaller image
        useCORS: true,
        backgroundColor: "#ffffff",
        windowWidth: 1024,
      });

      // Add cover page to PDF as JPEG (smaller size)
      const coverImgData = coverCanvas.toDataURL("image/jpeg", 0.7); // Use JPEG, quality 0.7
      const coverImgProps = pdf.getImageProperties(coverImgData);
      const coverHeight =
        (coverImgProps.height * pdfWidth) / coverImgProps.width;
      pdf.addImage(coverImgData, "JPEG", 0, 0, pdfWidth, coverHeight);

      // Capture questions section
      console.devAPILog("Capturing questions section...");
      (questionsSection as HTMLElement).style.display = "block";
      (questionsSection as HTMLElement).style.overflow = "visible";
      (questionsSection as HTMLElement).style.height = "auto";

      const questionsCanvas = await html2canvas(
        questionsSection as HTMLElement,
        {
          scale: 0.8, // Lower scale for smaller image
          useCORS: true,
          backgroundColor: "#ffffff",
          windowWidth: 1024,
          onclone: (clonedDoc) => {
            const clonedContent = clonedDoc.querySelector(
              ".flex-grow-1.overflow-auto"
            );
            if (clonedContent) {
              (clonedContent as HTMLElement).style.display = "block";
              (clonedContent as HTMLElement).style.overflow = "visible";
              (clonedContent as HTMLElement).style.height = "auto";
              console.devAPILog(
                "Cloned questions section prepared for capture"
              );
            }
          },
        }
      );

      pdf.addPage();
      const questionsImgData = questionsCanvas.toDataURL("image/jpeg", 0.7); // Use JPEG, quality 0.7
      const questionsImgProps = pdf.getImageProperties(questionsImgData);
      const questionsHeight =
        (questionsImgProps.height * pdfWidth) / questionsImgProps.width;

      if (questionsHeight > pdf.internal.pageSize.getHeight()) {
        console.devAPILog(
          "Questions section is tall, splitting across pages..."
        );
        const pageHeight = pdf.internal.pageSize.getHeight();
        let heightLeft = questionsHeight;
        let position = 0;
        let page = 1;

        pdf.addImage(
          questionsImgData,
          "JPEG",
          0,
          0,
          pdfWidth,
          questionsHeight,
          "",
          "FAST"
        );
        heightLeft -= pageHeight;
        position -= pageHeight;

        while (heightLeft > 0) {
          pdf.addPage();
          page++;
          console.devAPILog(`Adding page ${page} for questions section`);
          pdf.addImage(
            questionsImgData,
            "JPEG",
            0,
            position,
            pdfWidth,
            questionsHeight,
            "",
            "FAST"
          );
          heightLeft -= pageHeight;
          position -= pageHeight;
        }
      } else {
        pdf.addImage(questionsImgData, "JPEG", 0, 0, pdfWidth, questionsHeight);
      }

      // Capture inspector section
      console.devAPILog("Capturing inspector section...");
      const inspectorCanvas = await html2canvas(
        inspectorSection as HTMLElement,
        {
          scale: 0.8, // Lower scale for smaller image
          useCORS: true,
          backgroundColor: "#ffffff",
          windowWidth: 1024,
        }
      );

      pdf.addPage();
      const inspectorImgData = inspectorCanvas.toDataURL("image/jpeg", 0.7); // Use JPEG, quality 0.7
      const inspectorImgProps = pdf.getImageProperties(inspectorImgData);
      const inspectorHeight =
        (inspectorImgProps.height * pdfWidth) / inspectorImgProps.width;
      pdf.addImage(inspectorImgData, "JPEG", 0, 0, pdfWidth, inspectorHeight);

      // Hide the content again
      printableContentRef.current.style.display = "none";

      console.devAPILog("PDF created successfully with multiple pages");
      return pdf.output("blob");
    } catch (error) {
      console.error("Error in PDF generation:", error);
      // Make sure to hide the content even if there's an error
      if (printableContentRef.current) {
        printableContentRef.current.style.display = "none";
      }
      return null;
    }
  };

  useEffect(() => {
    if (report) {
      setSubject(
        `${report?.inspectionTemplateTitle} : ${
          report?.createdByUser?.firstName
        } ${report?.createdByUser?.lastName} : ${formatCustomDate(
          report?.createdAt
        )}`
      );
    }
  }, [report]);

  // Cleanup preview URL when component unmounts or modal closes
  useEffect(() => {
    return () => {
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
      }
    };
  }, [previewUrl]);

  // Preview method 2: Shows PDF in modal (alternative implementation)
  // To use this instead of new tab, replace onClick={handlePreview} with onClick={handlePreviewInModal}
  const handlePreviewInModal = async () => {
    setIsGeneratingPreview(true);
    try {
      const pdfBlob = await generatePDF();
      if (pdfBlob) {
        // Clean up previous URL if exists
        if (previewUrl) {
          URL.revokeObjectURL(previewUrl);
        }

        // Create new URL and show in modal
        const pdfUrl = URL.createObjectURL(pdfBlob);
        setPreviewUrl(pdfUrl);
        setShowPreview(true);
      } else {
        SwalMessage(
          null,
          "Failed to generate PDF preview",
          "Ok",
          "error",
          false
        );
      }
    } catch (error) {
      console.error("Error generating preview:", error);
      SwalMessage(
        null,
        "An error occurred while generating preview",
        "Ok",
        "error",
        false
      );
    } finally {
      setIsGeneratingPreview(false);
    }
  };

  const closePreview = () => {
    setShowPreview(false);
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
      setPreviewUrl(null);
    }
  };

  return (
    <>
      <Modal
        className="modal-right"
        scrollable={true}
        show={show}
        onHide={onHide}
      >
        {isLoading && <Loader />}
        <Modal.Header className="p-0 border-0">
          <Row className="w-100 align-items-center">
            <Col xs={10} className="mt-auto mb-auto">
              <h2 className="mb-0">Share Report</h2>
            </Col>
            <Col xs={2} className="text-end mb-3">
              <span className="close-btn cursor-pointer" onClick={onHide}>
                <RxCross2 fontSize={20} />
              </span>
            </Col>
          </Row>
        </Modal.Header>
        <Modal.Body className="p-0 mt-4">
          <Form>
            {/* <Form.Group className="mb-4">
              <Form.Label>Subject</Form.Label>
              <Form.Control
                type="text"
                value={subject}
                onChange={(e) => setSubject(e.target.value)}
                placeholder="Enter email subject"
                disabled
              />
            </Form.Group> */}

            <Form.Group className="mb-4">
              <Form.Label>Recipients</Form.Label>
              <RxReactSelect
                isMulti
                // isCreatable
                options={emailOptions}
                value={selectedEmails}
                onChange={(selected: any) =>
                  setSelectedEmails(selected as EmailOption[])
                }
                placeholder="Select email recipients..."
                className="basic-multi-select"
              />
            </Form.Group>

            {/* //TODO : Temp disable */}
            {/* <Form.Group className="mb-4">
              <Form.Label>Message (Optional)</Form.Label>
              <Form.Control
                as="textarea"
                rows={3}
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                placeholder="Enter your message"
                disabled={isLoading}
              />
            </Form.Group> */}

            {/* <Form.Group className="mb-4">
              <Form.Label>Attachments (Optional)</Form.Label>
              <RxMultiUpload
                accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png"
                onChange={() => {}}
                selectedFiles={selectedFiles}
                buttonText="Add Files"
                placeholderText="No files attached"
                multiple={true}
                maxFiles={5}
              />
              <Form.Text className="text-muted mt-2">
                You can attach up to 5 files (PDF, Word, Excel, or images)
              </Form.Text>
            </Form.Group> */}
          </Form>
        </Modal.Body>
        <Modal.Footer className="d-flex justify-content-between border-0 pt-4 px-0">
          <Button
            variant="outline-secondary"
            onClick={handlePreview}
            disabled={isLoading || isGeneratingPreview}
            className="px-4 d-flex align-items-center gap-2"
          >
            {isGeneratingPreview ? (
              <>
                <Spinner
                  as="span"
                  animation="border"
                  size="sm"
                  role="status"
                  aria-hidden="true"
                />
                Generating...
              </>
            ) : (
              <>
                <LuEye size={18} />
                Preview Report
              </>
            )}
          </Button>

          <Button
            variant="primary"
            onClick={handleSend}
            disabled={isLoading || isGeneratingPreview}
            className="px-4"
          >
            {isLoading ? (
              <>
                <Spinner
                  as="span"
                  animation="border"
                  size="sm"
                  role="status"
                  aria-hidden="true"
                  className="me-2"
                />
                Sending...
              </>
            ) : (
              "Send Email"
            )}
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Hidden div for printable content */}
      <div
        ref={printableContentRef}
        style={{
          display: "none",
          width: "1024px",
          backgroundColor: "#ffffff",
          fontFamily: "Arial, sans-serif",
        }}
      >
        <PrintableReport report={report} display={"block"} isDownloadMode />
      </div>

      {/* PDF Preview Modal */}
      {showPreview && previewUrl && (
        <Modal
          show={showPreview}
          onHide={closePreview}
          size="xl"
          centered
          className="pdf-preview-modal"
        >
          <Modal.Header closeButton>
            <Modal.Title>PDF Preview</Modal.Title>
          </Modal.Header>
          <Modal.Body className="p-0">
            <iframe
              src={previewUrl}
              width="100%"
              height="600px"
              style={{ border: "none" }}
              title="PDF Preview"
            />
          </Modal.Body>
          <Modal.Footer>
            <Button variant="secondary" onClick={closePreview}>
              Close
            </Button>
          </Modal.Footer>
        </Modal>
      )}
    </>
  );
};

export default ShareReportModal;
