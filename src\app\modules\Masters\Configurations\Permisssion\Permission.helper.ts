import axios from "axios";
import axiosInstance from "../../../../serverconfig/axiosInstance";
import { APIs } from "../../../../serverconfig/apiURLs";
import encryptDecryptUtil from "../../../../utils/encrypt-decrypt-util";

class PermissionService {
    getmoduleandactiondropdown(id: any) {
        const payload = {
            permissionId: id ? id : ""
        }
        const encPayload = encryptDecryptUtil.encryptData(
            JSON.stringify(payload),
            JSON.parse(localStorage.keyinfo).syckey
        )
        return axiosInstance.post(APIs.ALL_HEADERS.getmoduleandactiondropdown, {
            encryptedData: encPayload
        })
    }

    getcompaniesforpermission() {
        const keyinfo = JSON.parse(localStorage.keyinfo);
        const requestBody: any = {
            search: "",
            permissionId: ""
        }
        const encPayload = encryptDecryptUtil.encryptData(
            JSON.stringify(requestBody),
            keyinfo.syckey
        );
        return axiosInstance.post(APIs.ALL_HEADERS.getcompaniesforpermission, {
            encryptedData: encPayload
        })
    }

    createPermission(payload: any) {
        const keyinfo = JSON.parse(localStorage.keyinfo);
        const encPayload = encryptDecryptUtil.encryptData(
            JSON.stringify(payload),
            keyinfo.syckey
        );
        return axiosInstance.post(APIs.ALL_HEADERS.createPermission, {
            encryptedData: encPayload
        })
    }

    getPermissionGridData(page: number, size: number, search: string, status: string) {
        const keyinfo = JSON.parse(localStorage.keyinfo);
        const bodyparam = {
            page: page,
            size: size,
            search: search ? search : "",
            sortingColumns: [
                {
                    sortOrder: 0,
                    columnName: "",
                },
            ],
            active: JSON.parse(status),
        };
        const payload = encryptDecryptUtil.encryptData(
            JSON.stringify(bodyparam),
            keyinfo.syckey
        );
        return axiosInstance.post(APIs.GRIDCALLS.permissionGrid, {
            encryptedData: payload,
        });
    }
    getPermisssionEditData(permissionId: string) {
        const keyinfo = JSON.parse(localStorage.keyinfo);
        const requestBody: any = {
            permissionId: permissionId ? permissionId : "",
        }
        const payload = encryptDecryptUtil.encryptData(
            JSON.stringify(requestBody),
            keyinfo.syckey
        );
        return axiosInstance.post(APIs.ALL_HEADERS.getpermissiondataforedit, {
            encryptedData: payload
        })
    }

    getPermissions() {
        return axiosInstance.post(APIs.ALL_HEADERS.getpermissions)
    }

    getviewpermissiondata(permissionId: string) {
        const keyinfo = JSON.parse(localStorage.keyinfo);
        const requestBody: any = {
            permissionId: permissionId ? permissionId : "",
        }
        const payload = encryptDecryptUtil.encryptData(
            JSON.stringify(requestBody),
            keyinfo.syckey
        );
        return axiosInstance.post(APIs.ALL_HEADERS.getviewpermissiondata, {
            encryptedData: payload
        })
    }
    getmembersforpermission(permissionId: string, dropdown: any, search: any) {
        const keyinfo = JSON.parse(localStorage.keyinfo);
        const requestBody: any = {
            permissionId: permissionId ? permissionId : "",
            getForSelectedDropdown: dropdown,
            search: search ? search : ""
        }
        const payload = encryptDecryptUtil.encryptData(
            JSON.stringify(requestBody),
            keyinfo.syckey
        );
        return axiosInstance.post(APIs.ALL_HEADERS.getmembersforpermission, {
            encryptedData: payload
        })
    }

    saveMemberForPermission(permissionId: string, userIds: any[]) {
        const keyinfo = JSON.parse(localStorage.keyinfo);
        const requestBody: any = {
            permissionId: permissionId ? permissionId : "",
            userIds: userIds ? userIds : [],
        }
        const payload = encryptDecryptUtil.encryptData(
            JSON.stringify(requestBody),
            keyinfo.syckey
        );
        return axiosInstance.post(APIs.ALL_HEADERS.savememberforpermission, {
            encryptedData: payload
        })
    }
}

export const permissionService = new PermissionService();