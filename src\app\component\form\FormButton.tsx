import React from "react";

interface FormButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  highlightOnError?: boolean;
  errorSelector?: string;
  highlightClassName?: string;
  highlightDuration?: number;
}

/**
 * A button that, when clicked, scrolls to and highlights the first error element.
 * Useful for forms with validation errors.
 */
const FormButton: React.FC<FormButtonProps> = ({
  highlightOnError = false,
  errorSelector = ".form-error-message",
  highlightClassName = "highlight-error",
  highlightDuration = 1000,
  onClick,
  ...props
}) => {
  const handleClick = async (
    e: React.MouseEvent<HTMLButtonElement, MouseEvent>
  ) => {
    if (highlightOnError) {
      const firstError = document.querySelector(errorSelector);
      if (firstError) {
        setTimeout(() => {
          if (firstError) {
            firstError.scrollIntoView({ behavior: "smooth", block: "center" });
            // Use Intersection Observer to highlight only when in view
            if ('IntersectionObserver' in window) {
              const observer = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                  if (entry.isIntersecting) {
                    firstError.classList.add(highlightClassName);
                    setTimeout(() => {
                      firstError.classList.remove(highlightClassName);
                    }, highlightDuration);
                    observer.disconnect();
                  }
                });
              }, { threshold: 0.5 }); // 50% visible
              observer.observe(firstError);
            } else {
              // Fallback: highlight immediately
              firstError.classList.add(highlightClassName);
              setTimeout(() => {
                firstError.classList.remove(highlightClassName);
              }, highlightDuration);
            }
          }
        }, 0);
        return; // Don't call onClick if error found
      }
    }
    if (onClick) onClick(e);
  };

  return (
    <button {...props} onClick={handleClick}>
      {props.children}
    </button>
  );
};

export default FormButton;
