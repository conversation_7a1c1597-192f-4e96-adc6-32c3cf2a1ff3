import React, { useState, useEffect } from 'react';
import MediaTabContent from '../../../shared/component/MediaTabContent';
import { useDeleteKnowledgeBaseMutation, useGetKnowledgeBaseMutation, useUploadKnowledgeBaseMutation } from '../../../apis/inspectionKnowledgeAPI';
import { InspectionKnowledgeBaseItem } from '../../../apis/type';
import SwalMessage from '../../common/SwalMessage';
import Loader from '../../../component/Loader';
import { DocumentPreviewModal } from '../../../shared/component/FileUpload/PreviewModals';
import './style.css';

interface Props {
  templateId?: string;
}

const DocumentsTabDetails: React.FC<Props> = ({ templateId }) => {
  const [documents, setDocuments] = useState<InspectionKnowledgeBaseItem[]>([]);
  const [page, setPage] = useState<number>(0); // Start from page 0 as mentioned
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [previewModalOpen, setPreviewModalOpen] = useState<boolean>(false);
  const [previewDocumentUrl, setPreviewDocumentUrl] = useState<string>('');

  // RTK Query hooks with loading states
  const [getKnowledgeBase, { isLoading: isLoadingGet }] = useGetKnowledgeBaseMutation();
  const [uploadKnowledgeBase, { isLoading: isLoadingUpload }] = useUploadKnowledgeBaseMutation();
  const [deleteKnowledgeBase, { isLoading: isLoadingDelete }] = useDeleteKnowledgeBaseMutation();

  // Fetch documents when component mounts or templateId changes
  useEffect(() => {
    if (templateId) {
      // Reset pagination when templateId changes
      setPage(0);
      setDocuments([]);
      setHasMore(true);
      fetchDocuments(0, true);
    }
  }, [templateId]);

  // Fetch documents from API with pagination
  const fetchDocuments = async (pageNumber: number = 0, reset: boolean = false) => {
    if (!templateId) return;

    try {
      const response = await getKnowledgeBase({
        inspectionTemplateId: templateId,
        attachmentType: 'DOC',
        page: pageNumber,
        size: 200, // Smaller page size for better UX with infinite scroll
        search: '',
        sortingColumns: [{ sortOrder: 0, columnName: '' }]
      }).unwrap();

      // Update pagination state
      setPage(pageNumber);

      // Extract the actual data and pagination info from the nested structure
      const responseData = response.data || {};
      const totalCount = responseData.totalCount || 0;
      const newItems = responseData.data && Array.isArray(responseData.data) ? responseData.data : [];
      console.log("newItems========", newItems);
      // Check if there are more items to load
      setHasMore(newItems.length > 0 && (documents.length + newItems.length) < totalCount);

      // Update documents list - either replace or append
      if (reset) {
        setDocuments(newItems);
      } else {
        setDocuments(prevDocuments => [...prevDocuments, ...newItems]);
      }
    } catch (error) {
      console.error('Error fetching documents:', error);
      SwalMessage(null, 'Failed to load documents', 'Ok', 'error', false);
    }
  };

  // Load more documents when scrolling
  const loadMoreDocuments = () => {
    if (!isLoadingGet && hasMore) {
      fetchDocuments(page + 1);
    }
  };

  // Handle document upload
  const handleDocumentUpload = async (file: File, title: string, description: string) => {
    if (!templateId) {
      SwalMessage(null, 'Template ID is required', 'Ok', 'error', false);
      return false; // Return false to indicate upload failed
    }

    try {
      const response = await uploadKnowledgeBase({
        multipartFile: file, // This will be sent as 'file' in the FormData
        name: title,
        description: description,
        thumbnail: '',
        attachmentType: 'DOC',
        inspectionTemplateId: templateId
      }).unwrap();

      if (response && response.success) {
        SwalMessage(null, 'Document uploaded successfully', 'Ok', 'success', false);
        // Reset pagination and data before fetching new data
        setPage(0);
        setDocuments([]);
        setHasMore(true);
        // Fetch documents with reset flag to clear existing data
        fetchDocuments(0, true);
        // Modal will be closed by MediaTabContent component
        return true; // Return true to indicate upload succeeded
      } else {
        SwalMessage(null, response?.errormsg || 'Failed to upload document', 'Ok', 'error', false);
        return false; // Return false to indicate upload failed
      }
    } catch (error) {
      console.error('Error uploading document:', error);
      SwalMessage(null, 'Failed to upload document', 'Ok', 'error', false);
      return false; // Return false to indicate upload failed
    }
  };

  // Handle document deletion
  const handleDocumentDelete = async (id: string) => {
    // In a real implementation, you would call an API to delete the document
    // For now, just remove it from the local state
    // setDocuments(documents.filter(doc => doc.id.toString() !== id));

    // TODO: Implement API call to delete the document
    const response = await deleteKnowledgeBase({ id }).unwrap();

    // After deletion, reset and refresh the data to avoid pagination issues
    setPage(0);
    setHasMore(true);
    fetchDocuments(0, true);
  };

  // Handle document view - open PDF in preview modal
  const handleDocumentView = (id: string) => {
    const document = documents.find(doc => doc.id.toString() === id);
    if (document && document.url) {
      // Open in preview modal instead of new tab
      setPreviewDocumentUrl(document.url);
      setPreviewModalOpen(true);
    }
  };

  // Close preview modal
  const handleClosePreview = () => {
    setPreviewModalOpen(false);
    setPreviewDocumentUrl('');
  };

  return (
    <>
      {isLoadingGet && documents.length === 0 && <Loader />}
      {isLoadingDelete && <Loader />}
      
      <MediaTabContent
        title="TEMPLATE DOCUMENTS"
        mediaType="document"
        items={documents.map(doc => ({
          id: doc.id.toString(),
          name: doc.name || doc.displayName,
          description: doc.description,
          url: doc.url,
          thumbnail: doc.thumbnail || '',
          documentStatus: doc.documentStatus
        }))}
        onUpload={handleDocumentUpload}
        onDelete={handleDocumentDelete}
        onView={handleDocumentView}
        isLoading={isLoadingUpload}
        isLoadingMore={isLoadingGet && documents.length > 0}
        templateId={templateId}
        hasMore={hasMore}
        onLoadMore={loadMoreDocuments}
        showInfiniteScroll={true}
      />

      {/* Document Preview Modal */}
      <DocumentPreviewModal
        show={previewModalOpen}
        onClose={handleClosePreview}
        documentUrl={previewDocumentUrl}
      />
    </>
  );
};

export default DocumentsTabDetails;