import { ComboBox } from "@progress/kendo-react-dropdowns";
import { ChangeEvent, useEffect, useState } from "react";
import { Col, Form, Modal, Row } from "react-bootstrap";
import { BsSave } from "react-icons/bs";
import { SlClose, SlCloudUpload } from "react-icons/sl";
import { Link } from "react-router-dom";
import { toast } from "react-toastify";
import noimage from "../../../../efive_assets/images/noimage.jpg";
import { getUserDetail } from "../../../redux/userSlice";
import { APIs } from "../../../serverconfig/apiURLs";
import axiosInstance from "../../../serverconfig/axiosInstance";
import {
  AutoCapitalize,
  extensionPacks,
  findObjectByValue,
  formatContactInputNumber,
  getImage,
  imageOnError,
} from "../../../utils/CommonUtils";
import encryptDecryptUtil from "../../../utils/encrypt-decrypt-util";
import { useAppDispatch } from "../../../redux/store";
import SwalMessage from "../../common/SwalMessage";
import SingleSelectDropdown from "../../common/SingleSelectDropdown";
import { IoClose } from "react-icons/io5";
import { IoMdAdd } from "react-icons/io";
import validator from "validator";


type userDataRef = {
  editid: string;
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  userType: string;
  role: string;
  companyId?: string | undefined;
  userProfile: string | undefined;
  twoFactorRequired: string | undefined;
};

function Add_user_Modal({
  modal,
  setmodal,
  isEdit,
  userid,
  userEditData,
  fetchData,
  search,
  usertypeData,
  companyData,
  twoFAdata,
  pageTake,
  isOperationalFlag,
  pageNumber,
  setLoading,
}: any) {
  const dispatch = useAppDispatch();
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [email, setEmail] = useState("");
  const [phone, setPhone] = useState("");
  const [usertype, setUsertype] = useState<any>();
  const [company, setCompany] = useState<any>();
  const [twoFactorRequired, setTwoFactorRequired] = useState<any>({});
  const [base64String, setBase64String] = useState<string>("");
  const [imageUrl, setImageUrl] = useState<string>("");

  const [isFirstNameValid, setIsFirstNameValid] = useState(true);
  const [isLastNameValid, setIsLastNameValid] = useState(true);
  const [isEmailValid, setIsEmailValid] = useState(true);
  const [isPhoneValid, setIsPhoneValid] = useState(true);
  const [isUsertypeValid, setIsUsertypeValid] = useState(true);
  const [isCompanyValid, setIsCompanyValid] = useState(true);
  const [isTwoFactorRequiredValid, setIsTwoFactorRequiredValid] =
    useState(true);

  const [firstNameMsg, setFirstNameMsg] = useState("");
  const [lastNameMsg, setLastNameMsg] = useState("");
  const [emailMsg, setEmailMsg] = useState("");
  const [phoneMsg, setPhoneMsg] = useState("");
  const [usertypeMsg, setUsertypeMsg] = useState("");
  const [companyMsg, setCompanyMsg] = useState("");
  const [twoFactorRequiredMsg, setTwoFactorRequiredMsg] = useState("");

  const userinfo = localStorage.userinfo
    ? JSON.parse(localStorage.userinfo)
    : null;
    // console.log("object",userinfo)
  // const [usertypeData, setUsertypeData] = useState<any[]>([]);
  // const [companyData, setCompanyData] = useState<any[]>([]);
  // const [twoFAdata, setTwoFAdata] = useState<any[]>([]);

  // // Common Dropdown
  // const fetchUsertypeData = async () => {
  //   let keyinfo = JSON.parse(localStorage.keyinfo);
  //   const response = await axiosInstance.post(APIs.ALL_HEADERS.usertype, null);
  //   const resData = response.data;
  //   if (resData) {
  //     setUsertypeData(
  //       JSON.parse(encryptDecryptUtil.decryptData(resData.data, keyinfo.syckey))
  //     );
  //   }
  // };

  // const fetchTwoFactorData = async () => {
  //   let keyinfo = JSON.parse(localStorage.keyinfo);
  //   const response = await axiosInstance.post(APIs.ALL_HEADERS.twoFAdata, null);
  //   const resData = response.data;
  //   if (resData) {
  //     setTwoFAdata(
  //       JSON.parse(encryptDecryptUtil.decryptData(resData.data, keyinfo.syckey))
  //     );
  //   }
  // };

  // // Common Dropdown
  // const fetchCompanyData = async () => {
  //   let keyinfo = JSON.parse(localStorage.keyinfo);
  //   const response = await axiosInstance.post(APIs.ALL_HEADERS.company, null);
  //   const resData = response.data;
  //   if (resData) {
  //     setCompanyData(
  //       JSON.parse(encryptDecryptUtil.decryptData(resData.data, keyinfo.syckey))
  //     );
  //   }
  // };

  // useEffect(() => {
  //   if(modal){
  //     fetchUsertypeData();
  //     fetchCompanyData();
  //     fetchTwoFactorData();
  //   }
  // }, [modal]);

  useEffect(() => {
    if (!modal) {
      setBase64String("")
    }
  }, [modal])
  // const [loading, setLoading] = useState(false);
  const [isFormValid, setIsFormValid] = useState(false);

  const validForm = () => {
    if (
      firstName &&
      lastName &&
      email &&
      phone &&
      usertype &&
      Object.keys(usertype).length > 0 &&
      // Check company only if usertype is Company Admin
      (usertype.label !== "Company Admin" || (company && Object.keys(company).length > 0)) &&
      twoFactorRequired &&
      Object.keys(twoFactorRequired).length > 0
    ) {
      setIsFormValid(true);
    } else {
      setIsFormValid(false);
    }
  };

  useEffect(() => {
    validForm();
  }, [firstName, lastName, email, phone, usertype, company, twoFactorRequired]);

  useEffect(() => {
    if (isEdit) {
      setCompany(null);
      setFirstNameMsg("");
      setLastNameMsg("");
      setEmailMsg("");
      setPhoneMsg("");
      setUsertypeMsg("");
      setCompanyMsg("");
      setBase64String("");
      setFirstName(userEditData.first_name);
      setLastName(userEditData.last_name);
      setEmail(userEditData.email);
      setPhone(
        userEditData.phone_number
          ? formatContactInputNumber(userEditData.phone_number)
          : ""
      );
      setUsertype(findObjectByValue(userEditData.user_type, usertypeData));
      setCompany(findObjectByValue(userEditData.companyid, companyData));
      setTwoFactorRequired(
        findObjectByValue(userEditData.twoFactorRequired, twoFAdata)
      );
      setImageUrl(userEditData.userProfile ? getImage(userEditData.userProfile) : "");
    } else if (isEdit == false) {
      userEditData = undefined;
      setFirstNameMsg("");
      setLastNameMsg("");
      setEmailMsg("");
      setPhoneMsg("");
      setUsertypeMsg("");
      setCompanyMsg("");
      setFirstName("");
      setLastName("");
      setEmail("");
      setPhone("");
      setUsertype(usertypeData.find((item: any) => item.label === "Company Admin"));
      setCompany(null);
      setTwoFactorRequired({});
      setBase64String("");
      setTwoFactorRequired(
        twoFAdata.find((item: any) => userinfo?.companyTwoFaceAuth == 1 ? "" : item.label === "None")
      );
      setImageUrl("");
    }
  }, [isEdit, userEditData, modal, userid]);

  const handleUserType = (selectedOption: any) => {
    setUsertype(selectedOption.value);
  };
  const handleCompany = (selectedOption: any) => {
    setCompany(selectedOption.value);
  };
  const handleTwoFactorRequired = (selectedOption: any) => {
    setTwoFactorRequired(selectedOption.value);
  };

  // File Input 1
  const handleFileInputChange1 = (event: ChangeEvent<HTMLInputElement>) => {
    const fileSize: any = localStorage.getItem("userinfo");
    const newFileSize = JSON.parse(fileSize);
    const file: any = event.target.files?.[0];
    const isValidFileType = extensionPacks.imageExtensions.includes(file.type);
    const isValidFileSize =
      file?.size <= newFileSize.uploadimagesize * 1024 * 1024;
    if (file && isValidFileType && isValidFileSize) {
      const reader = new FileReader();
      reader.onloadend = () => {
        const result = reader.result;
        if (typeof result === "string") {
          setBase64String(result);
          setImageUrl(result);
          // console.log(result);
        }
      };
      reader.readAsDataURL(file);
    } else {
      if (!isValidFileType && file != undefined) {
        SwalMessage(
          null,
          "Please upload only jpeg | jpg | png file.",
          "Ok",
          "error",
          false
        );
      } else if (!isValidFileSize && file != undefined) {
        SwalMessage(
          null,
          `Please upload image under ${newFileSize.uploadimagesize}MB size.`,
          "Ok",
          "error",
          false
        );
      }
    }
    event.target.value = ""
  };

  // ** delete image
  const deleteimage = () => {
    SwalMessage(
      null,
      "Are you sure want to delete image?",
      "Yes",
      "warning",
      true
    ).then((result) => {
      if (result) {
        setImageUrl("");
        // setImageVisible(false);
      }
    });
  };

  // const fetchRoleData = async (usertypeid: string | null) => {
  //   let keyinfo = JSON.parse(localStorage.keyinfo);
  //   const obj = { id: usertypeid };
  //   let payload = encryptDecryptUtil.encryptData(
  //     JSON.stringify(obj),
  //     keyinfo.syckey
  //   );
  //   const response = await axiosInstance.post(APIs.ALL_HEADERS.role, {
  //     encryptedData: payload,
  //   });
  //   const resData = response.data;
  //   if (resData) {
  //     setRoleList(
  //       JSON.parse(encryptDecryptUtil.decryptData(resData.data, keyinfo.syckey))
  //         .datalist
  //     );
  //   }
  // };

  const validatePayload = () => {
    let isValid = true;
    if (firstName && firstName.trim()) {
      setIsFirstNameValid(true);
    } else {
      setIsFirstNameValid(false);
      setFirstNameMsg("Please Enter First Name.");
      // return;
      isValid = false;
    }
    if (lastName && lastName.trim()) {
      setIsLastNameValid(true);
    } else {
      setIsLastNameValid(false);
      setLastNameMsg("Please Enter Last Name.");
      // return;
      isValid = false;
    }
    if (!isEdit) {
      if (email && email.trim()) {
        const emailRegex = /\S+@\S+\.\S+/;
        if (validator.isEmail(email)) {
          setIsEmailValid(true);
        } else {
          setIsEmailValid(false);
          setEmailMsg("Please Enter Valid Email.");
          // return;
          isValid = false;
        }
      } else {
        setIsEmailValid(false);
        setEmailMsg("Please Enter Email.");
        // return;
        isValid = false;
      }
    }

    if (phone && phone.trim()) {
      // const contactRegex = /^[0-9]{10}$/;
      // if (contactRegex.test(phone)) {
      //   setIsPhoneValid(true);
      // } else {
      //   setIsPhoneValid(false);
      //   setPhoneMsg("Please Enter Valid Contact No.");
      //   return;
      // }
      setIsPhoneValid(true);
    } else {
      setIsPhoneValid(false);
      setPhoneMsg("Please Enter Phone No.");
      // return;
      isValid = false;
    }
    if (usertype && usertype.value) {
      setIsUsertypeValid(true);
    } else {
      setIsUsertypeValid(false);
      setUsertypeMsg("Please Select Role.");
      // return;
      isValid = false;
    }
    if (
      usertype &&
      usertype.value != "5953752b4664456f73714b586762787955774a6d6d513d3d"
    ) {
      if (company && company.value) {
        setIsCompanyValid(true);
      } else {
        setIsCompanyValid(false);
        setCompanyMsg("Please Select Company.");
        // return;
        isValid = false;
      }
    }
    if (twoFactorRequired && twoFactorRequired.value) {
      setIsTwoFactorRequiredValid(true);
    } else {
      setIsTwoFactorRequiredValid(false);
      setTwoFactorRequiredMsg("Please Select Configuration.");
      // return;
      isValid = false;
    }
    if (isValid) {
      const payload = {
        firstName: firstName?.trim(),
        lastName: lastName?.trim(),
        email: email,
        phoneNumber: phone ? phone.replace(/\D/g, "") : "",
        userType: usertype.value,
        userProfile: base64String ? base64String : imageUrl,
        twoFactorRequired: twoFactorRequired.value,
      } as userDataRef;
      usertype.value != "5953752b4664456f73714b586762787955774a6d6d513d3d"
        ? (payload["companyId"] = company.value)
        : "";
      isEdit == true ? (payload["editid"] = userid) : "";
      return payload;
    } else {
      return
    }
  };

  const handleSubmit = async () => {
    // validatePayload();
    const userinfo = JSON.parse(localStorage.userinfo);
    if (validatePayload()) {
      setLoading(true);
      const payload = validatePayload();
      // console.log(payload);
      let keyinfo = JSON.parse(localStorage.keyinfo);
      let ciphertext = encryptDecryptUtil.encryptData(
        JSON.stringify(payload),
        keyinfo.syckey
      );
      const response = await axiosInstance.post(APIs.ALL_HEADERS.createUser, {
        encryptedData: `${ciphertext}`,
      });
      const resData = response.data;
      if (resData.success == true) {
        if (userinfo.userid == payload?.editid) {
          await dispatch(getUserDetail({ userid: userinfo.userid })).unwrap();
        }
        setLoading(false);
        setmodal(false);
        // toast.success(resData.errormsg);
        SwalMessage(null, resData.errormsg, "Ok", "success", false).then((isConfirm) => {
          if (isConfirm) {
            fetchData(pageNumber, pageTake, search, null, null, null, null);
          }
        })
      } else {
        setLoading(false);
        // toast.error(resData.errormsg);
        SwalMessage(null, resData.errormsg, "Ok", "error", false);
      }
    }
  };
  return (
    <Modal className="modal-right" scrollable={true} show={modal}>
      {/* <Modal.Header className="p-0">
        <Row className=" ">
          <Col xs={6} className="mt-auto mb-auto">
            <h2 className="mb-0">{isEdit == true ? "Edit" : "Add"} User</h2>
          </Col>
          <Col xs={6} className=" text-end mb-3">
            {isEdit == true && isOperationalFlag == "no" ? (
              <></>
            ) : (
              <>
                <button
                  type="button"
                  className={`btn me-6  ${isFormValid ? "btn-success" : "rx-btn-disabled rx-btn"
                    }`}
                  onClick={handleSubmit}
                  disabled={!isFormValid}>
                  <BsSave className="btn-icon-custom" />
                  save
                
                </button>
              </>
            )}
            <span className="btn rx-btn" onClick={() => setmodal(false)}>
              <SlClose className="btn-icon-custom" />
              Close
            </span>
          </Col>
        </Row>
      </Modal.Header> */}
      <Modal.Header className="p-0">
        <Row>
          <Col sm={4} xs={4} className="mt-auto mb-auto">
            <h2 className="mb-0 mobile-margin">{isEdit == true ? "Edit" : "Add"} User</h2>
          </Col>
          <Col sm={8} xs={8} className=" text-end mb-3">
            {isEdit == true && isOperationalFlag == "no" ? (
              <></>
            ) : (
              <>
                <button
                  type="button"
                  className={`btn me-6  ${isFormValid ? "btn-success" : "rx-btn"
                    }`}
                  onClick={handleSubmit}
                // disabled={!isFormValid}
                >
                  <BsSave className="btn-icon-custom" />
                  save

                </button>
              </>
            )}
            <Link
              to={""}
              className="btn rx-btn"
              onClick={() => setmodal(false)}
            >
              <SlClose className="btn-icon-custom" />
              Close
            </Link>

          </Col>
        </Row>
      </Modal.Header>
      <Modal.Body className="p-0">
        <Row className="mt-5">
          <Col xxl={12} xl={12} lg={12} sm={12}>
            <Row>
              <Col xxl={12} xl={12} lg={12} sm={12} className="mb-5">
                <span className="modal-sub-title">User Information</span>
              </Col>

              <Col xxl={6} xl={6} lg={6} sm={12} className="mb-5">
                <Form.Label>
                  First Name<span className="text-danger">*</span>
                </Form.Label>
                <Form.Control
                  placeholder="Enter User Name"
                  value={firstName}
                  onChange={(e) => {
                    const value = e.target.value;
                    if (/^[^<>"',.]*$/.test(value)) {
                      setFirstName(AutoCapitalize(value));
                    }
                  }}
                  onBlur={(e: any) => {
                    const value = e.target.value;
                    if (/^[^<>"',.]*$/.test(value)) {
                      setFirstName(AutoCapitalize(value, true));
                    }
                  }}
                  maxLength={24}></Form.Control>
                {!isFirstNameValid ? (
                  <>
                    <span className="text-danger">{firstNameMsg}</span>
                  </>
                ) : (
                  ""
                )}
              </Col>

              <Col xxl={6} xl={6} lg={6} sm={12} className="mb-5">
                <Form.Label>
                  Last Name<span className="text-danger">*</span>
                </Form.Label>
                <Form.Control
                  placeholder="Enter User Name"
                  value={lastName}
                  onChange={(e) => {
                    const value = e.target.value;
                    if (/^[^<>"',.]*$/.test(value)) {
                      setLastName(AutoCapitalize(value));
                    }
                  }}
                  onBlur={(e) => {
                    const value = e.target.value;
                    if (/^[^<>"',.]*$/.test(value)) {
                      setLastName(AutoCapitalize(value, true));
                    }
                  }}
                  maxLength={24}></Form.Control>
                {!isLastNameValid ? (
                  <>
                    <span className="text-danger">{lastNameMsg}</span>
                  </>
                ) : (
                  ""
                )}
              </Col>

              <Col xxl={6} xl={6} lg={6} sm={12} className="mb-5">
                <Form.Label>
                  Email<span className="text-danger">*</span>
                </Form.Label>
                <Form.Control
                  placeholder="Enter Email"
                  value={email}
                  onChange={(e) => {
                    const values = e.target.value.replace(/[^a-zA-Z0-9@._-]/g, "");
                    setEmail(values?.toLowerCase());
                    if (values.trim() && validator.isEmail(values)) {
                      setIsEmailValid(true);
                      setEmailMsg("");
                    } else {
                      setIsEmailValid(false);
                      setEmailMsg("Please Enter Valid Email.");
                    }
                  }
                  }
                  maxLength={128}
                  disabled={isEdit ? true : false}></Form.Control>
                {!isEmailValid ? (
                  <>
                    <span className="text-danger">{emailMsg}</span>
                  </>
                ) : (
                  ""
                )}
              </Col>

              <Col xxl={6} xl={6} lg={6} sm={12} className="mb-5">
                <Form.Label>
                  Phone<span className="text-danger">*</span>
                </Form.Label>
                <Form.Control
                  placeholder="Enter Phone"
                  value={phone}
                  onChange={(e) =>
                    setPhone(formatContactInputNumber(e.target.value))
                  }
                  // value={phoneNumber}
                  // onChange={(e) => {setPhoneNumber(formatContactInputNumber(e.target.value))}}
                  maxLength={14}></Form.Control>
                {!isPhoneValid ? (
                  <>
                    <span className="text-danger">{phoneMsg}</span>
                  </>
                ) : (
                  ""
                )}
              </Col>

              <Col xxl={6} xl={6} lg={6} sm={12} className="mb-5">
                <Form.Label>
                  Role<span className="text-danger">*</span>
                </Form.Label>
                {/* <ComboBox
                  className="form-control"
                  data={usertypeData}
                  onChange={handleUserType}
                  value={usertype}
                  placeholder="Select Role"
                  filterable={true}
                  // onFilterChange={filterChange}
                  textField="label"
                  dataItemKey="value"
                  allowCustom={true}
                /> */}
                <SingleSelectDropdown
                  data={usertypeData}
                  getter={usertype}
                  setter={setUsertype}
                  placeholder="Select Role"
                />
                {!isUsertypeValid ? (
                  <>
                    <span className="text-danger">{usertypeMsg}</span>
                  </>
                ) : (
                  ""
                )}
              </Col>

              {usertype?.value !=
                "5953752b4664456f73714b586762787955774a6d6d513d3d" ? (
                <>
                  <Col xxl={6} xl={6} lg={6} sm={12} className="mb-5">
                    <Form.Label>
                      Company<span className="text-danger">*</span>
                    </Form.Label>

                    {/* <ComboBox
                      className="form-control"
                      data={companyData}
                      onChange={handleCompany}
                      value={company}
                      placeholder="Select Company"
                      filterable={true}
                      // onFilterChange={filterChange}
                      textField="label"
                      dataItemKey="value"
                      allowCustom={true}
                    /> */}
                    <SingleSelectDropdown
                      data={companyData}
                      getter={company}
                      setter={setCompany}
                      placeholder="Select Company"
                    />
                    {!isCompanyValid ? (
                      <>
                        <span className="text-danger">{companyMsg}</span>
                      </>
                    ) : (
                      ""
                    )}
                  </Col>
                </>
              ) : (
                ""
              )}
              <Col xxl={6} xl={6} lg={6} sm={12} className="mb-5">
                <Form.Label>
                  2FA Configuration<span className="text-danger">*</span>
                </Form.Label>
                {/* <ComboBox
                  className="form-control"
                  data={twoFAdata}
                  onChange={handleTwoFactorRequired}
                  value={twoFactorRequired}
                  placeholder="Select Configuration"
                  filterable={true}
                  // onFilterChange={filterChange}
                  textField="label"
                  dataItemKey="value"
                  allowCustom={true}
                /> */}
                <SingleSelectDropdown
                  data={twoFAdata}
                  getter={twoFactorRequired}
                  setter={setTwoFactorRequired}
                  placeholder="Select Configuration"
                />
                {!isTwoFactorRequiredValid ? (
                  <>
                    <span className="text-danger">{twoFactorRequiredMsg}</span>
                  </>
                ) : (
                  ""
                )}
              </Col>
            </Row>
            <Row>
              <Col xxl={6} xl={6} lg={6} sm={6} className="mb-5 position-relative">
                <Form.Label>User Profile </Form.Label>
                {/* <div>
                  <label htmlFor="file-upload">
                    <span className="btn rx-btn cursor-pointer">
                      <SlCloudUpload className="btn-icon-custom" />
                      Upload
                      <input
                        id="file-upload"
                        type="file"
                        accept=".jpeg, .jpg, .png"
                        onChange={handleFileInputChange1}
                        style={{ display: "none" }}
                      />
                    </span>
                  </label>
                </div>
                <img
                  className="image-input-wrapper mt-4"
                  src={imageUrl ? imageUrl : noimage}
                  onError={imageOnError}></img> */}
                <div className="mt-2">
                  <img
                    src={imageUrl ? imageUrl : noimage}
                    className="image-input-wrapper ms-4"
                  />
                  <label htmlFor="file-upload-dark">
                    <span className="upload-btn me-2 ">
                      <IoMdAdd className="me-1 mt-3 add-icon" />
                      <input
                        id="file-upload-dark"
                        type="file"
                        accept=".jpeg, .jpg, .png"
                        onChange={handleFileInputChange1}
                        style={{ display: "none" }}
                      />
                    </span>
                  </label>
                  {imageUrl && (
                    <span className="upload-btn me-2">
                      <IoClose
                        className="me-1 mt-3 add-icon"
                        onClick={deleteimage}
                      />
                    </span>
                  )}
                </div>
              </Col>
            </Row>
          </Col>
        </Row>
      </Modal.Body >
    </Modal >
  );
}

export default Add_user_Modal;
