import React, { useEffect } from "react";
import { useFormikContext } from "formik";
import { QuestionFormValues, generateRatingOptions } from "../addQuestions/util/constant";
import FormSelect from "../../../../component/form/FormSelect";
import FormErrorMessage from "../../../../component/form/FormErrorMessage";
import { LuSmile, LuStar, LuThumbsUp } from "react-icons/lu";
import FormLabel from "../../../../component/form/FormLabel";

interface RatingIconOption {
  label: string;
  value: string;
  icon: React.ReactNode;
}

const RatingViewer: React.FC = () => {
  const { values, setFieldValue } = useFormikContext<QuestionFormValues>();

  const maxRatingOptions = Array.from({ length: 10 }, (_, i) => ({
    label: String(i + 1),
    value: i + 1,
  }));

  const ratingIconOptions: RatingIconOption[] = [
    { label: "Smiley", value: "smiley", icon: <LuSmile size={24} /> },
    { label: "Star", value: "star", icon: <LuStar size={24} /> },
    { label: "Thumbs Up", value: "thumb_up", icon: <LuThumbsUp size={24} /> },
  ];

  // Generate options when rating value changes
  useEffect(() => {
    if (values.ratting !== undefined && values.ratting > 0) {
      const ratingOptions = generateRatingOptions(values.ratting);
      setFieldValue('options', ratingOptions);
    }
  }, [values.ratting, setFieldValue]);

  return (
    <div className="rating-viewer">
      <div className="row">
        <div className="col-md-6 mb-3">
          <FormLabel className="form-label">Max Rating</FormLabel>
          <FormSelect
            name="ratting"
            options={maxRatingOptions}
            placeholder="Select max rating"
          />
          <FormErrorMessage name="ratting" />
        </div>
        <div className="col-md-6 mb-3">
          <FormLabel className="form-label">Rating Icon</FormLabel>
          <FormSelect
            name="rattingIcon"
            options={ratingIconOptions}
            placeholder="Select rating icon"
            formatOptionLabel={(data: unknown) => {
              const option = data as RatingIconOption;
              return (
                <div className="d-flex align-items-center gap-2">
                  {option.icon}
                  <span>{option.label}</span>
                </div>
              );
            }}
          />
          <FormErrorMessage name="rattingIcon" />
        </div>
      </div>
      {values.ratting && values.rattingIcon && (
        <>
          <label className="form-label">Preview</label>
          <div className="response-viewer-box-wrapper p-3">
            <div className="d-flex gap-2">
              {Array.from({ length: values.ratting }, (_, i) => {
                const Icon =
                  values.rattingIcon === "smiley"
                    ? LuSmile
                    : values.rattingIcon === "star"
                    ? LuStar
                    : LuThumbsUp;
                return (
                  <div key={i}>
                    <Icon size={20}/>
                  </div>
                );
              })}
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default RatingViewer;
