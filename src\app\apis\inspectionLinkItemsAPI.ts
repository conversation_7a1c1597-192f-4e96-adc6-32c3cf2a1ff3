import { createApi } from "@reduxjs/toolkit/query/react";
import { fetchRxInspectionAPI } from "../redux/fetchBase";
import { APIs } from "../serverconfig/apiURLs";
import {
  IResponse,
  GetLinkedTicketsPayload,
  GetLinkedTicketsResponse,
  LinkTicketsPayload,
  LinkWebLinksPayload,
  DeleteLinkWeblinkPayload
} from "./type";

export const inspectionLinkItemsAPI = createApi({
  reducerPath: "inspectionLinkItemsAPI",
  baseQuery: fetchRxInspectionAPI(),
  tagTypes: ["InspectionLinkItems"],
  endpoints: (builder) => ({
    getLinkedTickets: builder.mutation<
      IResponse<GetLinkedTicketsResponse>,
      GetLinkedTicketsPayload
    >({
      query: (payload) => ({
        url: APIs.INSPECTION_LINK_ITEMS.GET_LINKED_TICKETS,
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["InspectionLinkItems"],
    }),

    linkTickets: builder.mutation<IResponse<any>, LinkTicketsPayload>({
      query: (payload) => ({
        url: APIs.INSPECTION_LINK_ITEMS.LINK_TICKETS,
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["InspectionLinkItems"],
    }),

    linkWebLinks: builder.mutation<IResponse<any>, LinkWebLinksPayload>({
      query: (payload) => ({
        url: APIs.INSPECTION_LINK_ITEMS.LINK_WEB_LINKS,
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["InspectionLinkItems"],
    }),

    deleteLinkWeblink: builder.mutation<IResponse<any>, DeleteLinkWeblinkPayload>({
      query: (payload) => ({
        url: APIs.INSPECTION_LINK_ITEMS.DELETE_LINK_WEBLINK,
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["InspectionLinkItems"],
    }),
  }),
});

export const {
  useGetLinkedTicketsMutation,
  useLinkTicketsMutation,
  useLinkWebLinksMutation,
  useDeleteLinkWeblinkMutation,
} = inspectionLinkItemsAPI;
