import { Field, FieldArray, FormikProps } from "formik";
import React, { useEffect, useCallback } from "react";
import FormLabel from "../../component/form/FormLabel";
import { IFormValues } from "./inspection";
import FormErrorMessage from "../../component/form/FormErrorMessage";
import { MdOutlineDeleteForever } from "react-icons/md";
import { FiPlus } from "react-icons/fi";
import FormSelect from "../../component/form/FormSelect";
import { pointOption } from "./constant";
import { StylesConfig } from "react-select";
import SwalMessage from "../common/SwalMessage";

interface Props {
  formik: FormikProps<IFormValues>;
  viewOnly?: boolean;
}

const dot = (color = "transparent") => ({
  alignItems: "center",
  display: "flex",

  ":before": {
    backgroundColor: color,
    borderRadius: 10,
    content: '" "',
    display: "block",
    marginRight: 8,
    height: 10,
    width: 10,
  },
});
const gradPointStyle: StylesConfig<any> = {
  input: (styles) => ({ ...styles, ...dot() }),
  placeholder: (styles) => ({ ...styles, ...dot("#ccc") }),
  singleValue: (styles, { data }) => ({ ...styles, ...dot(data.color) }),
};

const GradPointSection: React.FC<Props> = ({ formik, viewOnly = false }) => {
  const { values } = formik;

  // Common function to calculate total points
  const calculateTotalPoints = useCallback(
    (excludeIndex?: number) => {
      return values.inspectionTemplateGradingOptionRequestDtos.reduce(
        (sum, option, index) => {
          // Skip the excluded index and N/A values
          if (
            (excludeIndex !== undefined && index === excludeIndex) ||
            option.gradingOptionPoint === "N/A" ||
            option.gradingOptionPoint === ""
          ) {
            return sum;
          }
          return sum + (parseInt(option.gradingOptionPoint) || 0);
        },
        0
      );
    },
    [values.inspectionTemplateGradingOptionRequestDtos]
  );

  // Common function to show error message for exceeding points
  const showPointsExceededError = useCallback((isAddingNew = false) => {
    const message = isAddingNew
      ? "You cannot add more options as the total points would exceed 100."
      : "The sum of all grading option points cannot exceed 100. Please adjust your selections.";

    const title = isAddingNew ? "Total Points Limit" : "Total Points Exceeded";

    SwalMessage(title, message, "OK", isAddingNew ? "warning" : "error", false);
  }, []);

  // Validate total points whenever values change
  // useEffect(() => {
  //   if (viewOnly) return; // Skip validation in view-only mode

  //   const totalPoints = calculateTotalPoints();

  //   // Check if total exceeds 100
  //   if (totalPoints > 100) {
  //     showPointsExceededError();
  //   }
  // }, [
  //   values.inspectionTemplateGradingOptionRequestDtos,
  //   viewOnly,
  //   calculateTotalPoints,
  //   showPointsExceededError,
  // ]);

  // Function to validate if the sum of selected points exceeds 100
  const validateTotalPoints = useCallback(
    (newPoint: string, currentIndex: number) => {
      // Skip validation for N/A values
      if (newPoint === "N/A") return true;

      // Calculate the sum of all selected points excluding the current one
      const totalPoints = calculateTotalPoints(currentIndex);

      // Add the new point value
      const newTotal = totalPoints + parseInt(newPoint);

      // Check if the total exceeds 100
      // if (newTotal > 100) {
      //   showPointsExceededError();
      //   return false;
      // }

      return true;
    },
    [calculateTotalPoints, showPointsExceededError]
  );

  // Custom handler for point selection
  const handlePointChange = useCallback(
    (option: any, actionMeta: any) => {
      // Extract the index from the field name
      const fieldNameMatch = actionMeta?.name?.match(/\[(\d+)\]/);
      if (!fieldNameMatch) return;

      const gradPointsIndex = parseInt(fieldNameMatch[1]);
      const value = option?.value;

      // Only validate when a value is selected
      // if (value && !validateTotalPoints(value, gradPointsIndex)) {
      //   // If validation fails, reset to empty value
      //   setTimeout(() => {
      //     formik.setFieldValue(
      //       `inspectionTemplateGradingOptionRequestDtos[${gradPointsIndex}].gradingOptionPoint`,
      //       ""
      //     );
      //   }, 0);
      // }
    },
    [validateTotalPoints, formik]
  );

  return (
    <div className="block-card">
      <FieldArray name="inspectionTemplateGradingOptionRequestDtos">
        {(gradPointFieldArray) => (
          <>
            <FormLabel style={{ display: "inline-block", marginBottom: 10 }}>
              Grading & Points
            </FormLabel>
            {values.inspectionTemplateGradingOptionRequestDtos.map(
              (_grad, gradPointsIndex: number) => {
                const filteredPointOptions = pointOption.filter(
                  (option) =>
                    !values.inspectionTemplateGradingOptionRequestDtos.some(
                      (selectedOption, index) =>
                        index !== gradPointsIndex &&
                        selectedOption.gradingOptionPoint === option.value
                    )
                );

                return (
                  <div key={gradPointsIndex} className="mt-4">
                    <div className="question-no-wrapper gap-3 align-items-start">
                      <div className="grad-gradingOptionPoint-box gap-3 w-100">
                        <div className="w-100">
                          <FormLabel>Option {gradPointsIndex + 1}</FormLabel>
                          <Field
                            name={`inspectionTemplateGradingOptionRequestDtos[${gradPointsIndex}].gradingOptionName`}
                            placeholder="Enter Question Text"
                            className="form-control w-100"
                            disabled={viewOnly}
                            readOnly={viewOnly}
                          />
                          <FormErrorMessage
                            name={`inspectionTemplateGradingOptionRequestDtos[${gradPointsIndex}].gradingOptionName`}
                          />
                        </div>
                        <div className="point-wrapper">
                          <FormLabel>Point</FormLabel>
                          <FormSelect
                            options={filteredPointOptions}
                            styles={gradPointStyle}
                            name={`inspectionTemplateGradingOptionRequestDtos[${gradPointsIndex}].gradingOptionPoint`}
                            isDisabled={viewOnly}
                            onChange={handlePointChange}
                          />
                        </div>
                      </div>
                      {!viewOnly &&
                        values.inspectionTemplateGradingOptionRequestDtos
                          .length > 1 && (
                          <button
                            type="button"
                            className="btn btn-sm btn-icon"
                            onClick={() =>
                              gradPointFieldArray.remove(gradPointsIndex)
                            }
                            style={{ paddingTop: "45px" }}
                          >
                            <MdOutlineDeleteForever className="fs-2" />
                          </button>
                        )}
                    </div>
                  </div>
                );
              }
            )}

            {!viewOnly && (
              <button
                type="button"
                className="btn btn-blue-black rx-btn mt-5 d-flex align-items-center justify-content-center gap-2"
                onClick={() => {
                  // Calculate current total points
                  // const totalPoints = calculateTotalPoints();

                  // // Check if adding more options would potentially exceed 100 points
                  // if (totalPoints >= 100) {
                  //   showPointsExceededError(true);
                  //   return;
                  // }

                  // Add new option
                  gradPointFieldArray.push({
                    gradingOptionName: "",
                    gradingOptionPoint: "",
                  });
                }}
              >
                <FiPlus /> New Option
              </button>
            )}
          </>
        )}
      </FieldArray>
    </div>
  );
};

export default GradPointSection;
