import { orderBy } from "@progress/kendo-data-query";
import { GridColumn as Column, Grid } from "@progress/kendo-react-grid";
import { Tooltip } from "@progress/kendo-react-tooltip";
import { useEffect, useState } from "react";
import { FaFileDownload, FaRegEye, FaRegFileExcel } from "react-icons/fa";
import { IoMdAdd, IoMdMore } from "react-icons/io";
import { Link, useNavigate } from "react-router-dom";
import { ToastContainer, toast } from "react-toastify";
import Swal from "sweetalert2";
import { APIs } from "../../../serverconfig/apiURLs";
import axiosInstance from "../../../serverconfig/axiosInstance";
import {
  formatContactInputNumber,
  formatPhoneNumber,
  getImage,
  imageOnError,
  swalMessages,
} from "../../../utils/CommonUtils";
import encryptDecryptUtil from "../../../utils/encrypt-decrypt-util";
import ResetPasswordModal from "../../authentication/Reset_password_Modal";
import SwalMessage from "../../common/SwalMessage";
import Add_user_Modal from "./Add_user_Modal";
import { userService } from "./user.helper";
import { useBreadcrumbContext } from "../../../../_metronic/layout/components/header/BreadcrumbsContext";
import {
  MdLockReset,
  MdOutlineDeleteForever,
  MdOutlineEdit,
} from "react-icons/md";
import { Col, Dropdown, Row } from "react-bootstrap";
import { FilterSvg, SortingSvg } from "../../../utils/SvgUtils";
import FilterModal from "./FilterModal";
import ViewPasscodeModal from "./ViewPasscodeModal";
import Spinner from "../../common/Spinner";

function UserMasterKendo() {
  const navigate = useNavigate();
  const [resetPasswordModal, setResetPasswordModal] = useState(false);
  const [usertypeData, setUsertypeData] = useState<any[]>([]);
  const [companyData, setCompanyData] = useState<any[]>([]);
  const [twoFAdata, setTwoFAdata] = useState<any[]>([]);
  const [viewPasscode, setViewPasscode] = useState<boolean>(false);
  const [gridData, setGridData] = useState<any[]>([]);

  const [isEdit, setIsEdit] = useState(false);
  const [userEditData, setUserEditData] = useState("");
  const [userid, setUserid] = useState("");
  const [isOperationalFlag, setIsOperationalFlag] = useState("yes");

  const [loading, setLoading] = useState<Boolean>(false);
  const [search, setSearch] = useState("");
  const [modal, setmodal] = useState(false);

  const [totalCount, setTotalCount] = useState<any>();

  const initialSort: Array<any> = [{ field: "saudh", dir: "desc" }];
  const [perPage] = useState(10);
  const [totalRows, setTotalRows] = useState(0);
  const [sort, setSort] = useState(initialSort);
  const [showFileterModal, setshowFilterModal] = useState<boolean>(false);
  const [filterCount, setFiltercount] = useState<any>(0);
  const [passcode, setPasscode] = useState("");
  const [filterData, setFilterData] = useState({
    status: "",
    usertype: "",
  });
  const initialDataState: any = { skip: 0, take: 10 };
  const [page, setPage] = useState<any>(initialDataState);
  const [pageSizeValue, setPageSizeValue] = useState<
    number | string | undefined
  >(initialDataState.take);
  const pageNumber = Math.floor(page.skip / page.take) + 1;

  const pageChange = (event: any) => {
    const { skip, take } = event.page;
    const targetEvent = event.targetEvent as any;
    const newTake = targetEvent.value == "All" ? totalCount : take;
    const newPageSizeValue = targetEvent.value == "All" ? "All" : take;

    setPage({ skip, take: newTake });
    setPageSizeValue(newPageSizeValue);
    // console.log("Page size value:", newPageSizeValue);
  };
  useEffect(() => {
    if (search) {
      setPage(initialDataState);
    }
  }, [search]);
  // Initial Call

  useEffect(() => {
    const pageNumber = Math.floor(page.skip / page.take) + 1;
    // console.log("filterData", filterData);

    fetchData(
      pageNumber,
      page.take,
      search,
      null,
      null,
      filterData?.status,
      filterData?.usertype
    );
  }, [search, page, filterData]);

  useEffect(() => {
    fetchUsertypeData();
    fetchCompanyData();
    fetchTwoFactorData();
  }, []);

  // Common Dropdown
  const fetchUsertypeData = async () => {
    let keyinfo = JSON.parse(localStorage.keyinfo);
    const response = await axiosInstance.post(APIs.ALL_HEADERS.usertype, null);
    const resData = response.data;
    if (resData) {
      setUsertypeData(
        JSON.parse(encryptDecryptUtil.decryptData(resData.data, keyinfo.syckey))
      );
    }
  };

  const fetchTwoFactorData = async () => {
    let keyinfo = JSON.parse(localStorage.keyinfo);
    const response = await axiosInstance.post(APIs.ALL_HEADERS.twoFAdata, null);
    const resData = response.data;
    if (resData) {
      setTwoFAdata(
        JSON.parse(encryptDecryptUtil.decryptData(resData.data, keyinfo.syckey))
      );
    }
  };

  // Common Dropdown
  const fetchCompanyData = async () => {
    let keyinfo = JSON.parse(localStorage.keyinfo);
    const response = await axiosInstance.post(APIs.ALL_HEADERS.company, null);
    const resData = response.data;
    if (resData) {
      setCompanyData(
        JSON.parse(encryptDecryptUtil.decryptData(resData.data, keyinfo.syckey))
      );
    }
  };

  // Edit Function
  const handleedit = async (id: any, isoperational: any) => {
    try {
      setLoading(true);
      let keyinfo = JSON.parse(localStorage.keyinfo);
      const response = await userService
        .getEditData(id)
        .then((response: any) => {
          setLoading(false);
          return response.data;
        })
        .catch((e: any) => {
          console.info(e);
          if (e.response.status == 401) {
            // localStorage.removeItem("islogin");
            navigate("/admindashboard");
            // navigate(0);
          }
          // toast.error(e.message);
          SwalMessage(null, e.message, "Ok", "error", false);
        });

      if (response.success == true) {
        setmodal(true);
        setUserEditData(
          JSON.parse(
            encryptDecryptUtil.decryptData(response.data, keyinfo.syckey)
          )
        );
        setUserid(id);
        setIsEdit(true);
        setIsOperationalFlag(isoperational);
      } else if (response.success == false) {
        // toast.error(response.errormsg);
        SwalMessage(null, response.errormsg, "Ok", "error", false);
      }
    } catch (error) {
      console.error("Error fetching data:", error);
      // toast.error("Something Went Wrong.");
      SwalMessage(null, "Something Went Wrong.", "Ok", "error", false);
    }
  };

  // Delete Function
  const handleDelete = async (id: any) => {
    const confirmed = await SwalMessage(
      swalMessages.title.commonTitle,
      swalMessages.text.deleteUserMsg,
      swalMessages.confirmButtonText.delete,
      swalMessages.icon.error,
      true
    );
    if (confirmed) {
      try {
        setLoading(true);
        const response = await userService
          .deleteData(id)
          .then((response: any) => {
            setLoading(false);
            return response.data;
          })
          .catch((e: any) => {
            console.info(e);
            if (e.response.status == 401) {
              navigate("/admindashboard");
            }
            // toast.error(e.message);
            SwalMessage(null, e.message, "Ok", "error", false);
          });
        if (response.success == true) {
          SwalMessage(
            null,
            response.errormsg,
            "Ok",
            "success",
            false
          )
            // Swal.fire({
            //   title: "Deleted",
            //   text: response.errormsg,
            //   icon: "success",
            //   confirmButtonColor: "#3085d6",
            // })
            .then(() => {
              fetchData(
                1,
                page.take,
                search,
                null,
                null,
                filterData?.status,
                filterData?.usertype
              );
            });
        } else {
          // toast.error(response.errormsg);
          SwalMessage(null, response.errormsg, "Ok", "error", false);
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    }
  };

  var fetchData = async (
    page: any,
    size: any,
    search: any,
    sortOrder: any,
    columnName: any,
    status: any,
    userType: any
  ) => {
    let keyinfo = JSON.parse(localStorage.keyinfo);
    try {
      setLoading(true);
      // console.log("status", status);

      await userService
        .getGridData(
          page,
          size,
          search,
          sortOrder,
          columnName,
          status,
          userType
        )
        .then((response: any) => {
          if (response.status == 200) {
            const responseData = response.data;
            if (responseData.success == true) {
              const decryptedData = encryptDecryptUtil.decryptData(
                responseData.data,
                keyinfo.syckey
              );
              const resultData = JSON.parse(decryptedData);

              // const filteredData = resultData?.data.filter(
              //   (user: any) => user.id !== loggedInUser.userid
              // );
              // console.log("resultData", resultData);

              setLoading(false);
              // setUserGridData(filteredData);
              setGridData(resultData?.data);
              // console.log(wholeData);
              setTotalCount(resultData?.totalCount);
              setTotalRows(resultData?.recordsTotal);
            } else {
              setLoading(false);
              SwalMessage(null, responseData?.errormsg, "Ok", "error", false);
            }
          }
        })
        .catch((error) => {
          if (error?.response?.status == 401) {
            // localStorage.removeItem("islogin");
            navigate("/admindashboard");
            // navigate(0);
          }
          setLoading(false);
          SwalMessage(null, error?.message, "Ok", "error", false);
        })
        .finally(() => {
          setLoading(false);
        });
    } catch (error) {
      console.error("Error fetching data:", error);
    }
  };

  const changeStatus = async (id: any, data: any) => {
    const newStatus = data?.status === "Active" ? "Inactive" : "Active";
    const confirmed = await SwalMessage(
      `Are You Sure?`,
      `Do you really want to change the status of ${data?.userdisplayname} to ${newStatus}?`,
      swalMessages.confirmButtonText.change,
      swalMessages.icon.info,
      true
    );
    if (confirmed) {
      try {
        setLoading(true);
        const response = await userService
          .changeStatus(id)
          .then((response: any) => {
            return response.data;
          })
          .catch((e: any) => {
            console.info(e);
            if (e.response.status == 401) {
              navigate("/admindashboard");
            }
            // toast.error(e.message);
            SwalMessage(null, e.message, "Ok", "error", false);
          });
        if (response) {
          if (response.success == true) {
            setLoading(false);
            SwalMessage(
              null,
              response.errormsg,
              "Ok",
              "success",
              false
            ).then(() => {
              // setPage(initialDataState);
              fetchData(
                pageNumber,
                page.take,
                search,
                null,
                null,
                filterData?.status,
                filterData?.usertype
              );
            });
          } else {
            SwalMessage(
              null,
              response.errormsg,
              "Ok",
              "error",
              false
            )
          }
        } else {
          SwalMessage(
            null,
            "Something Went Wrong.",
            "Ok",
            "error",
            false
          )

        }
      } catch (error) {
        console.error("Error fetching data:", error);
        // toast.error("Something Went Wrong.");
        SwalMessage(null, "Something Went Wrong.", "Ok", "error", false);
        setLoading(false);
      }
    }
  };

  const excelexport = async () => {
    setLoading(true);
    try {
      const date = new Date();
      await userService
        .excelExport(search)
        .then((response) => {
          setLoading(false);
          const url = window.URL.createObjectURL(new Blob([response.data]));
          const link = document.createElement("a");
          link.href = url;
          link.setAttribute(
            "download",
            `User_${date.getFullYear() + "_" + date.getMonth() + "_" + date.getDate()
            }.xlsx`
          );
          document.body.appendChild(link);
          link.click();
          window.URL.revokeObjectURL(url);
        })
        .then((e: any) => {
          console.info(e);
          if (e.response.status == 401) {
            // localStorage.removeItem("islogin");
            navigate("/admindashboard");
            // navigate(0);
          }
          // toast.error(e.message);
          SwalMessage(null, e.message, "Ok", "error", false);
        });
    } catch (error) {
      console.error("Error downloading data:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleAdd = () => {
    setmodal(true);
    setIsEdit(false);
  };

  // start switch
  const renderswitch = (id: any, data: any) => {
    // console.log("status", data)
    return (
      <td>
        <div className="d-flex align-items-center">
          <div className="me-3">
            <input
              id={id}
              type="checkbox"
              className="checkbox"
              checked={data?.status == "Active" ? false : true}
              onChange={() => changeStatus(id, data)}
            />

            <label htmlFor={id} className="switch">
              <span className="switch__circle">
                <span className="switch__circle-inner"></span>
              </span>
              <span className="switch__left">Active</span>
              <span className="switch__right">Inactive</span>
            </label>
          </div>
        </div>
      </td>
    );
  };

  // start action
  const handleResendPasscode = (id: any) => {
    setLoading(true);
    const payload = { userid: id };
    userService
      .resendadminPasscode(payload)
      .then((response: any) => {
        if (response.status == 200) {
          // console.log("resposne", response);
          const responseData = response.data;
          if (responseData.success == true) {
            // console.log("responseData", responseData);
            SwalMessage(null, responseData?.errormsg, "Ok", "success", false);
            setLoading(false);
          } else {
            setLoading(false);
            SwalMessage(null, responseData?.errormsg, "Ok", "error", false);
          }
        }
      })
      .catch((error) => {
        if (error?.response?.status == 401) {
          // localStorage.removeItem("islogin");
          navigate("/admindashboard");
          // navigate(0);
        }
        setLoading(false);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleViewPasscode = (id: any) => {
    setLoading(true);
    let keyinfo = JSON.parse(localStorage.keyinfo);
    const payload = {
      userid: id,
    };
    userService
      .viewadminPasscode(payload)
      .then((response: any) => {
        if (response.status == 200) {
          // console.log("resposne", response);
          const responseData = response.data;
          if (responseData.success == true) {
            const decryptedData = encryptDecryptUtil.decryptData(
              responseData.data,
              keyinfo.syckey
            );
            // console.log("responseData", responseData);
            const resultData = JSON.parse(decryptedData);
            setPasscode(resultData?.passcode);
            // console.log("resultData", resultData);
            setViewPasscode(true);
            setLoading(false);
          } else {
            setLoading(false);
            SwalMessage(null, responseData?.errormsg, "Ok", "error", false);
          }
        }
      })
      .catch((error) => {
        if (error?.response?.status == 401) {
          // localStorage.removeItem("islogin");
          navigate("/admindashboard");
          // navigate(0);
        }
        setLoading(false);
      })
      .finally(() => {
        setLoading(false);
      });
  };
  const renderaction = (
    id: any,
    disableDelete: any,
    isoperational: any,
    newuser: any,
    role: any
  ) => {
    // console.log("role", role);

    const handleEditClick = () => {
      handleedit(id, isoperational);
    };
    const handleDeleteClick = () => {
      handleDelete(id);
    };
    const openResetModal = () => {
      setResetPasswordModal(true);
      setUserid(id);
    };

    const { setLabels } = useBreadcrumbContext();
    useEffect(() => {
      setLabels([{ path: "", state: {}, breadcrumb: "Admin User" }]);
    }, []);

    return (
      <td>
        {/* <div className="text-center menu-hover">
          <a
            className="text-center"
            data-kt-menu-trigger="hover"
            data-kt-menu-placement="bottom-end"
          >
            <IoMdMore className="td-icon cursor-pointer" />
          </a>
          <div
            className="menu menu-sub menu-sub-dropdown w-120px w-md-120px mt-1"
            data-kt-menu="true"
          >
            <div className="menu-item">
              {role !== "Super Admin" && (
                <a className="menu-link">
                  <span className="menu-title " onClick={handleEditClick}>
                    <MdOutlineEdit className="me-4" />
                    Edit
                  </span>
                </a>
              )}
              {(disableDelete as number) == 0 && isoperational == "yes" ? (
                <>
                  <a className="menu-link ">
                    <span className="menu-title" onClick={handleDeleteClick}>
                      <MdOutlineDeleteForever className="me-4" />
                      Delete
                    </span>
                  </a>
                </>
              ) : (
                ""
              )}
              {newuser === 0 && (
                <a className="menu-link">
                  <span className="menu-title" onClick={openResetModal}>
                    <MdLockReset className="me-4" />
                    Reset Password
                  </span>
                </a>
              )}
              {newuser === 1 && (
                <>
                  <a className="menu-link">
                    <span
                      className="menu-title"
                      onClick={() => handleResendPasscode(id)}
                    >
                      <MdLockReset className="me-4" />
                      Resend Passcode
                    </span>
                  </a>
                  <a className="menu-link">
                    <span
                      className="menu-title"
                      onClick={() => handleViewPasscode(id)}
                    >
                      <MdLockReset className="me-4" />
                      View Passcode
                    </span>
                  </a>
                </>
              )}
            </div>
          </div>
        </div> */}
        <Dropdown className="new-chat-btn" style={{ position: 'static' }}>
          <Dropdown.Toggle
            as="span"
            className="fs-1 cursor-pointer ms-2"
          >
            <IoMdMore className="td-icon cursor-pointer" />
          </Dropdown.Toggle>
          <Dropdown.Menu align="end">
            {role !== "Super Admin" && (
              <Dropdown.Item onClick={handleEditClick}>
                <span className="fs-5 " >
                  <MdOutlineEdit className="me-4" size={18} />
                  Edit
                </span>
              </Dropdown.Item>
            )}
            {(disableDelete as number) == 0 && isoperational == "yes" ? (
              <>
                <Dropdown.Item onClick={handleDeleteClick}>
                  <span className="fs-5" >
                    <MdOutlineDeleteForever className="me-4" size={18} />
                    Delete
                  </span>
                </Dropdown.Item>
              </>
            ) : (
              ""
            )}
            {newuser === 0 && (
              <Dropdown.Item onClick={openResetModal}>
                <span className="fs-5" >
                  <MdLockReset className="me-4" size={18} />
                  Reset Password
                </span>
              </Dropdown.Item>
            )}
            {newuser === 1 && (
              <>
                <Dropdown.Item onClick={() => handleResendPasscode(id)}>
                  <span
                    className="fs-5"
                  >
                    <MdLockReset className="me-4" size={18} />
                    Resend Passcode
                  </span>
                </Dropdown.Item>
                <Dropdown.Item onClick={() => handleViewPasscode(id)}>
                  <span
                    className="fs-5"
                  >
                    <FaRegEye className="me-4" size={16} />
                    View Passcode
                  </span>
                </Dropdown.Item>
              </>
            )}
          </Dropdown.Menu>
        </Dropdown>
      </td>
    );
  };
  // end action

  // start image
  const renderimage = (image: any) => {
    return (
      <td>
        <img
          src={getImage(image)}
          style={{
            width: "35px",
            height: "35px",
            border: "3px solid rgb(0 0 0 / 8%)",
          }}
          onError={imageOnError}
          className="user-image"
        ></img>
      </td>
    );
  };

  //  start tooltip
  const renderTooltipCell = (props: any) => {
    const { dataItem, field, content } = props;
    return (
      <td
        className="k-table-td cursor-pointer"
        onClick={() =>
          handleedit(dataItem.efar_user_id, dataItem.isoperational)
        }
      >
        <span className="ellipsis-cell" title={content}>
          {dataItem[field]}
        </span>
      </td>
    );
  };

  const CustomIDCell = (props: { dataItem: any }) => {
    const { dataItem } = props;
    return (
      <td
        onClick={() =>
          handleedit(dataItem.efar_user_id, dataItem.isoperational)
        }
        className="k-table-td cursor-pointer"
      >
        {dataItem.viewid}
        {/* <span title={dataItem.viewid}>
        {dataItem.viewid}
          </span> */}
      </td>
    );
  };
  // end tooltip

  const renderphone = (props: any) => {
    const { dataItem, field } = props;
    const formattedPhoneNumber = formatContactInputNumber(dataItem[field]);
    return (
      <>
        <td
          className="k-table-td cursor-pointer"
          onClick={() =>
            handleedit(dataItem.efar_user_id, dataItem.isoperational)
          }
        >
          <svg
            style={{ height: "14px", width: "14px", marginRight: "3px" }}
            className="td-icon"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 512 512"
          >
            <path d="M497.4 361.8l-112-48a24 24 0 0 0 -28 6.9l-49.6 60.6A370.7 370.7 0 0 1 130.6 204.1l60.6-49.6a23.9 23.9 0 0 0 6.9-28l-48-112A24.2 24.2 0 0 0 122.6 .6l-104 24A24 24 0 0 0 0 48c0 256.5 207.9 464 464 464a24 24 0 0 0 23.4-18.6l24-104a24.3 24.3 0 0 0 -14-27.6z" />
          </svg>
          {/* <span title={formatPhoneNumber(content)}> */}
          {/* {formatPhoneNumber(dataItem[field])} */}
          {/* </span> */}
          <span title={formattedPhoneNumber}>{formattedPhoneNumber}</span>
        </td>
      </>
    );
  };

  return (
    <>
      {loading && <Spinner />}
      <Row className="mb-7">
        {/* <Col xl={5} lg={4} md={4} sm={4} className="mt-auto mb-auto">
          <h1 className="page-title mobile-margin mb-0">Admin User</h1>
        </Col> */}
        <Col xl={3} lg={3} md={3} sm={12} className="mobile-margin">
          <div className="d-flex justify-content-end align-items-center gap-4">
            <input
              type="text"
              className="form-control "
              placeholder="Search..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              autoFocus
            />
            <div
              className="user-image filter-div text-center mt-3 cursor-pointer"
              onClick={() => setshowFilterModal(true)}
            >
              <FilterSvg width="16" height="16" className="svgicon" />
              <div className="filter-count">
                <span>{filterCount}</span>
              </div>
            </div>
          </div>
        </Col>
        <Col xl={9} lg={9} md={9} sm={12} className="text-end">
          <span className="btn rx-btn me-4" onClick={excelexport}>
            <FaFileDownload className="me-2" />
            Export
          </span>
          <span className="btn rx-btn" onClick={handleAdd}>
            <IoMdAdd className="me-2" />
            Add
          </span>
        </Col>
      </Row>
      <div className="card mt-0">
        <div className="card-body p-0">
          <div className="table_div ">
            <Tooltip position="bottom" anchorElement="target">
              <Grid
                data={orderBy(gridData, sort)}
                skip={page.skip}
                take={page.take}
                total={totalCount}
                pageable={{
                  buttonCount: 4,
                  pageSizes: [10, 25, 50, 100, "All"],
                  pageSizeValue: pageSizeValue,
                }}
                onPageChange={pageChange}
                sortable={true}
                sort={sort}
                onSortChange={(e: any) => {
                  setSort(e.sort);
                }}
              >
                <Column
                  title=""
                  headerClassName="center-header"
                  cell={(props) => renderimage(props.dataItem.userProfile)}
                  width={"65px"}
                  sortable={false}
                />
                <Column
                  field="viewid"
                  title="ID"
                  width="110px"
                  cell={CustomIDCell}
                />
                <Column
                  field="companyname"
                  title="Company"
                  cell={(props) =>
                    renderTooltipCell({
                      ...props,
                      content: props.dataItem.companyname,
                    })
                  }
                // width={"80px"}
                />
                <Column
                  field="userdisplayname"
                  cell={(props) =>
                    renderTooltipCell({
                      ...props,
                      content: props.dataItem.userdisplayname,
                    })
                  }
                  title="Name"
                />
                <Column
                  field="email"
                  title="Email"
                  cell={(props) =>
                    renderTooltipCell({
                      ...props,
                      content: props.dataItem.email,
                    })
                  }
                />
                <Column
                  field="phone_number"
                  title="Phone"
                  cell={(props) =>
                    renderphone({
                      ...props,
                      content: props.dataItem.phone_number,
                    })
                  }
                  width={"165px"}
                />
                {/* <Column
                  field="usertype"
                  title="User Type"
                  cell={(props) =>
                    renderTooltipCell({
                      ...props,
                      content: props.dataItem.usertype,
                    })
                  }
                /> */}
                <Column
                  field="rolename"
                  title="Role"
                  cell={(props) =>
                    renderTooltipCell({
                      ...props,
                      content: props.dataItem.rolename,
                    })
                  }
                />
                <Column
                  title="Status"
                  width={"100px"}
                  cell={(props) =>
                    renderswitch(
                      props.dataItem.efar_user_id,
                      props.dataItem
                    )
                  }
                  headerClassName="test-class"
                  className="d-flex align-items-center justify-content-center"
                />
                <Column
                  title="Action"
                  cell={(props) =>
                    renderaction(
                      props.dataItem.efar_user_id,
                      props.dataItem.disabledelete,
                      props.dataItem.isoperational,
                      props.dataItem.newuser,
                      props.dataItem.rolename
                    )
                  }
                  width={"80px"}
                  sortable={false}
                />
              </Grid>
            </Tooltip>
          </div>
        </div>
      </div>
      <Add_user_Modal
        modal={modal}
        setmodal={setmodal}
        isEdit={isEdit}
        userid={userid}
        userEditData={userEditData}
        fetchData={fetchData}
        usertypeData={usertypeData}
        companyData={companyData}
        twoFAdata={twoFAdata}
        search={search}
        pageTake={page.take}
        isOperationalFlag={isOperationalFlag}
        setLoading={setLoading}
        pageNumber={pageNumber}
      />

      <ResetPasswordModal
        resetPasswordModal={resetPasswordModal}
        setResetPasswordModal={setResetPasswordModal}
        showClose={true}
        userid={userid}
      />
      <ViewPasscodeModal
        viewPasscode={viewPasscode}
        setViewPasscode={setViewPasscode}
        passcode={passcode}
      />

      <FilterModal
        showFileterModal={showFileterModal}
        setshowFilterModal={setshowFilterModal}
        filterData={filterData}
        usertypeData={usertypeData}
        setFilterData={setFilterData}
        setFiltercount={setFiltercount}
      />
    </>
  );
}

export default UserMasterKendo;
