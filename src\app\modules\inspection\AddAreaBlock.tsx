import React from "react";
import { <PERSON>Array, FormikProps } from "formik";
import { DragDropContext, Draggable, Droppable } from "react-beautiful-dnd";
import AreaHeader from "./AreaHeader";
import QuestionSection from "./QuestionSection";
import { FiPlus } from "react-icons/fi";
import { IFormValues } from "./inspection";
import AreaAttchmentInput from "./AreaAttchmentInput";

interface Props {
  formik: FormikProps<IFormValues>;
  viewOnly?: boolean;
}

const AddAreaBlock: React.FC<Props> = ({ formik, viewOnly = false }) => {
  const { values, setFieldValue } = formik;

  const handleBlockReorder = (result: any) => {
    if (!result.destination) return;

    const reorderedBlocks = [...values.inspectionTemplateAreaRequestDtos];
    const [removed] = reorderedBlocks.splice(result.source.index, 1);
    reorderedBlocks.splice(result.destination.index, 0, removed);

    // Update order to maintain uniqueness
    reorderedBlocks.forEach((block, index) => (block.areaOrder = index + 1));

    setFieldValue("inspectionTemplateAreaRequestDtos", reorderedBlocks);
  };

  return (
    <FieldArray name="inspectionTemplateAreaRequestDtos">
      {(blockFieldArray) => (
        <>
          {viewOnly ? (
            <div>
              {values.inspectionTemplateAreaRequestDtos
                .sort((a, b) => a.areaOrder - b.areaOrder)
                .map((block, blockIndex) => (
                  <div key={block.areaOrder} className="block-card p-0">
                    <div className="p-5 pb-0 position-relative">
                      <AreaHeader
                        blockIndex={blockIndex}
                        fieldArray={blockFieldArray}
                        provided={null}
                        length={values.inspectionTemplateAreaRequestDtos.length}
                        viewOnly={viewOnly}
                      />
                      <div className="area-attachment-input-wrapper">
                        <AreaAttchmentInput
                          blockIndex={blockIndex}
                          list={block.inspectionTemplateAreaAttachmentDto}
                          formik={formik}
                          viewOnly={viewOnly}
                        />
                      </div>
                    </div>
                    <QuestionSection
                      block={block}
                      blockIndex={blockIndex}
                      formik={formik}
                      viewOnly={viewOnly}
                    />
                  </div>
                ))}
            </div>
          ) : (
            <DragDropContext onDragEnd={handleBlockReorder}>
              <Droppable droppableId="inspectionTemplateAreaRequestDtos">
                {(provided) => {
                  return (
                    <div {...provided.droppableProps} ref={provided.innerRef}>
                      {values.inspectionTemplateAreaRequestDtos
                        .sort((a, b) => a.areaOrder - b.areaOrder)
                        .map((block, blockIndex) => (
                          <Draggable
                            key={block.areaOrder}
                            draggableId={`block-${block.areaOrder}`}
                            index={blockIndex}
                          >
                            {(provided) => (
                              <div
                                ref={provided.innerRef}
                                {...provided.draggableProps}
                                className="block-card p-0"
                              >
                                <div className="p-5 pb-0 position-relative">
                                  <AreaHeader
                                    blockIndex={blockIndex}
                                    fieldArray={blockFieldArray}
                                    provided={provided}
                                    length={
                                      values.inspectionTemplateAreaRequestDtos
                                        .length
                                    }
                                    viewOnly={viewOnly}
                                  />
                                  <div className="area-attachment-input-wrapper">
                                    <AreaAttchmentInput
                                      blockIndex={blockIndex}
                                      list={
                                        block.inspectionTemplateAreaAttachmentDto
                                      }
                                      formik={formik}
                                      viewOnly={viewOnly}
                                    />
                                  </div>
                                </div>
                                <QuestionSection
                                  block={block}
                                  blockIndex={blockIndex}
                                  formik={formik}
                                  viewOnly={viewOnly}
                                />
                              </div>
                            )}
                          </Draggable>
                        ))}
                      {provided.placeholder}
                    </div>
                  );
                }}
              </Droppable>
            </DragDropContext>
          )}

          {!viewOnly && (
            <>
              <button
                type="button"
                className="btn btn-blue-black rx-btn d-flex align-items-center justify-content-center mb-5 gap-2"
                onClick={() => {
                  const currentAreas = values.inspectionTemplateAreaRequestDtos;
                  const maxOrder = currentAreas.length
                    ? Math.max(...currentAreas.map((a) => a.areaOrder))
                    : 0;
                  blockFieldArray.push({
                    areaOrder: maxOrder + 1,
                    areaName: "",
                    inspectionTemplateQuestionRequestDtos: [
                      {
                        questionOrder: 1,
                        questionText: "",
                        inspectionTemplateQuestionAttachmentDto: [],
                      },
                    ],
                  });
                }}
              >
                <FiPlus /> New Area
              </button>
            </>
          )}
        </>
      )}
    </FieldArray>
  );
};

export default AddAreaBlock;
