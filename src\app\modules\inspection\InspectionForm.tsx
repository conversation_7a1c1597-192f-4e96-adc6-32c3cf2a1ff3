import React, { useState } from "react";
import { FormikProps } from "formik";
import { IFormValues } from "./inspection";
import { Col, Row, Button } from "react-bootstrap";
import GradPointSection from "./GradPointSection";
import AddAreaBlock from "./AddAreaBlock";
import InspectionHeader from "./InspectionHeader";
import { useNavigate } from "react-router";
import TemplateHistory from "./TemplateHistory";
import LoopVendorManager from "./LoopVendorManager";
import InspectionCSVModal from "./InspectionCSVModal";
import { FaFileImport } from "react-icons/fa";
import ScrollToTopBottom from "../../shared/component/ScrollToTopBottom";
import useInspectionForm from "./useInspectionForm"; // Import to get validation schema
import { useAutoSave } from "../../shared/hooks/useAutoSave";
import AutosaveLoader from "../../shared/component/AutosaveLoader";
import FormButton from "../../component/form/FormButton";

interface Props {
  formik: FormikProps<IFormValues>;
  isTemplateEdit?: boolean;
  viewOnly?: boolean;
  handleFormAutoSave?: (formData: IFormValues) => void;
  isAutoSaveFailed?: boolean;
  isAutoSaveDisable?: boolean;
  isAutoSaveHappened?: boolean;
  handleSubmitWithConfirmation: (
    formData: IFormValues,
    type: "draft" | "publish"
  ) => void;
}

const InspectionTemplateForm: React.FC<Props> = ({
  formik,
  isTemplateEdit,
  viewOnly = false,
  handleFormAutoSave,
  isAutoSaveFailed,
  isAutoSaveDisable,
  isAutoSaveHappened,
  handleSubmitWithConfirmation,
}) => {
  const navigate = useNavigate();
  const { setFieldValue } = formik;
  const [showCSVModal, setShowCSVModal] = useState(false);

  // Get validation schema for auto-save
  const { validationSchema } = useInspectionForm();

  // Custom auto-save function for inspection form
  const handleAutoSave = async (formData: IFormValues) => {
    console.log("Auto-saving inspection form data:", formData);

    // Create form data with status as DRAFT for auto-save
    const formDataWithDraftStatus = {
      ...formData,
      inspectionTemplateStatus: "DRAFT",
    };

    await handleFormAutoSave?.(formDataWithDraftStatus);
  };

  // Initialize auto-save hook
  const { isSaving } = useAutoSave<IFormValues>({
    formik,
    validationSchema,
    saveFunction: handleAutoSave,
    intervalMs: 3000,
    isEnabled: !viewOnly || !isAutoSaveDisable, // Only enable auto-save when not in view-only mode
  });

  const handleOpenCSVModal = () => {
    setShowCSVModal(true);
  };

  const handleCloseCSVModal = () => {
    setShowCSVModal(false);
  };

  return (
    <>
      {/* Auto-save status indicator */}
      <AutosaveLoader isSaving={isSaving} isError={isAutoSaveFailed} />

      <Row>
        <Col lg={7} md={12}>
          <div className="d-flex justify-content-between align-items-center mb-3">
            <h4 className="m-0">Inspection Template</h4>
          </div>
          <InspectionHeader formik={formik} viewOnly={viewOnly} />
          <AddAreaBlock formik={formik} viewOnly={viewOnly} />
        </Col>
        <Col lg={5} md={12}>
          <GradPointSection formik={formik} viewOnly={viewOnly} />
          {formik?.values?.propertyIds && (
            <LoopVendorManager
              formik={formik}
              viewOnly={viewOnly}
              isTemplateEdit={isTemplateEdit}
            />
          )}
          {formik?.values?.templateHistory && !viewOnly && (
            <TemplateHistory
              tempHistory={formik?.values?.templateHistory}
              templateName={formik?.values?.inspectionTemplateName}
            />
          )}
        </Col>
      </Row>
      {[...Array(21)].map((_, i) => (
        <br key={i} />
      ))}
      {/* CSV Import Modal */}
      {showCSVModal && (
        <InspectionCSVModal
          show={showCSVModal}
          onClose={handleCloseCSVModal}
          formik={formik}
        />
      )}

      {!viewOnly && (
        <div className="row save-continue-footer mt-5">
          <div className="col-12">
            <div className="row custom-card py-2 px-4 d-flex justify-content-end border-0">
              {!viewOnly && !isTemplateEdit && (
                <div className="col-sm-6">
                  <div className="d-flex">
                    <span
                      className="btn rx-btn rx-btn-primary d-flex align-items-center gap-2"
                      onClick={handleOpenCSVModal}
                    >
                      <FaFileImport /> Import From CSV
                    </span>
                  </div>
                </div>
              )}
              <div className="col-sm-6 text-end">
                <span
                  className="btn rx-btn btn-black-lightblue me-4"
                  onClick={() => navigate("/inspections/manage-templates")}
                >
                  {isAutoSaveHappened ? "Back" : "Cancel"}
                </span>
                <>
                  <button
                    type="button"
                    className={"btn btn-success"}
                    style={{
                      background: "var(--bs-warning) !important",
                      borderColor: "var(--bs-warning) !important",
                      marginRight: "15px",
                    }}
                    onClick={() => {
                      setFieldValue("inspectionTemplateStatus", "DRAFT");
                      const finalValue = {
                        ...formik.values,
                        inspectionTemplateStatus: "DRAFT",
                      };
                      handleSubmitWithConfirmation(finalValue, "draft");
                    }}
                  >
                    Save as Draft
                  </button>

                  <FormButton
                    type="button"
                    className={"btn btn-success"}
                    highlightOnError={true}
                    onClick={() => {
                      setFieldValue("inspectionTemplateStatus", "PUBLISH");
                      formik.handleSubmit();
                    }}
                  >
                    Publish
                  </FormButton>
                </>
              </div>
            </div>
          </div>
        </div>
      )}
      <ScrollToTopBottom />
    </>
  );
};

export default InspectionTemplateForm;
