import React, { useEffect, useRef } from "react";
import FormLabel from "../../component/form/FormLabel";
import { Field, FormikProps } from "formik";
import FormErrorMessage from "../../component/form/FormErrorMessage";
import { Col, Row } from "react-bootstrap";
import { useGetPropertiesListMutation } from "../../apis/propertiesListAPI";
import { useGetAssignDepartmentListMutation } from "../../apis/departmentListAPI";
import { IFormValues } from "./inspection";
import { FormSelect } from "../../component/form/inedx";
import { priorityOption } from "./constant";
import { autoCapitalizeWithSpecialChar } from "../../utils/CommonUtils";

interface Props {
  formik: FormikProps<IFormValues>;
  viewOnly?: boolean;
}

const InspectionHeader: React.FC<Props> = ({ formik, viewOnly = false }) => {
  const { setFieldValue, values } = formik;
  const isDepartmentFetchInitially = useRef<Boolean>(false) //*When in edit mode 

  const permissiontype = JSON.parse(
    localStorage.getItem("userdetail") as string
  )?.permission;
  const isPropertyBaseUser = permissiontype !== "D";
  const [
    getPropertiesList,
    { data: propertyList, isLoading: isPropertyLoading },
  ] = useGetPropertiesListMutation();

  const [
    getAssignDepartmentList,
    { data: departmentList, isLoading: isDepartmentLoading },
  ] = useGetAssignDepartmentListMutation();

  const handlePropertyChange = (option: any) => {
    if (isPropertyBaseUser) {
    } else {
      setFieldValue("departmentIds", []);
      getAssignDepartmentList({ propertyid: option.value });
    }
  };

  useEffect(() => {
    getPropertiesList({});
  }, []);


  useEffect(() => {
    if(!isDepartmentFetchInitially.current){
      if (values.propertyIds && !isPropertyBaseUser) {
        getAssignDepartmentList({ propertyid: values.propertyIds as string });
        isDepartmentFetchInitially.current = true
      }
    }
  },[values.propertyIds])

  return (
    <div className="block-card">
      <div className="w-100 mt-0 pt-0 question-container">
        <FormLabel>Template Name</FormLabel>
        <Field
          name={`inspectionTemplateName`}
          placeholder="Template Name"
          className="form-control"
          disabled={viewOnly}
          readOnly={viewOnly}
        >
          {({ field, form }: any) => (
            <input
              {...field}
              placeholder="Template Name"
              className="form-control"
              disabled={viewOnly}
              readOnly={viewOnly}
              onChange={(e) => {
                const value = autoCapitalizeWithSpecialChar(e.target.value);
                form.setFieldValue(field.name, value);
              }}
            />
          )}
        </Field>
        <FormErrorMessage name={`inspectionTemplateName`} />
      </div>

      <div className="w-100 question-container">
        <FormLabel>Priority</FormLabel>
        <FormSelect
          name="priority"
          options={priorityOption}
          isDisabled={viewOnly}
        />
      </div>

      <Row>
        <Col sm={12} className="mt-1 pt-3">
          <FormLabel>Property Name</FormLabel>
          <FormSelect
            isMulti={isPropertyBaseUser}
            name="propertyIds"
            options={
              propertyList?.data?.map?.(
                (item: { propertyid: any; propertyname: string }) => ({
                  value: item.propertyid,
                  label: item.propertyname.trim(),
                })
              ) || []
            }
            onChange={handlePropertyChange}
            isLoading={isPropertyLoading}
            isDisabled={viewOnly}
          />
          {permissiontype === "D" && (
            <Col sm={12} className="mt-1 pt-3">
              <FormLabel>Department Name</FormLabel>
              <FormSelect
                isMulti
                name="departmentIds"
                options={
                  departmentList?.data?.map(
                    (item: { departmentId: any; departmentName: string }) => ({
                      value: item.departmentId,
                      label: item.departmentName.trim(),
                    })
                  ) || []
                }
                isLoading={isDepartmentLoading}
                isDisabled={viewOnly}
              />
            </Col>
          )}
        </Col>
      </Row>
      {/* <Row>
        <Col lg={6} md={12}>
          <div className="w-100 question-container">
            <FormLabel>Frequency</FormLabel>
            <FormSelect />
            <FormErrorMessage name={`frequency`} />
          </div>
        </Col>
        <Col lg={6} md={12}>
          <div className="w-100 question-container">
            <FormLabel>Loop Vendor</FormLabel>
            <FormSelect />
            <FormErrorMessage name={`loopVendor`} />
          </div>
        </Col>
      </Row> */}
    </div>
  );
};

export default InspectionHeader;
