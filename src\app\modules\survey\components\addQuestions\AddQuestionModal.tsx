import React, { useState } from "react";
import { RxCross2 } from "react-icons/rx";
import { Col, Modal, Row } from "react-bootstrap";
import useQuestionForm, { responseViewerFelid } from "./hooks/useQuestionForm";
import AllowAttachmentType from "./AllowAttchemntType";
import FormInput from "../../../../component/form/FormInput";
import FormSwitch from "../../../../component/form/FormSwitch";
import FormLabel from "../../../../component/form/FormLabel";
import ResponseTypeDropdown from "./ResponseTypeDropdown";
import ResponseViewer from "../responseTypeCreation/ResponseViewer";
import BranchingSection from "./BranchingSection";
import { BranchingFormValues } from "./hooks/useBranchingForm";
import { QuestionFormValues } from "./util/constant";
import QuestionPreviewModal from "./QuestionPreviewModal";
import { useAddQUestionToSurveyMutation } from "../../../../apis/survaysAPI";
import useSurveyUtil from "../../helper/useDetectSurvayType";
import { FormikProps } from "formik";
import SwalMessage from "../../../common/SwalMessage";
import AutoTicketOptionsSelector from "./AutoTicketOptionsSelector";

interface Props {
  isOpen: boolean;
  onClose: () => void;
  questionNumber?: number;
  initialValues?: QuestionFormValues & Partial<BranchingFormValues>;
  onSubmit: (values: QuestionFormValues & Partial<BranchingFormValues>) => void;
  formik: FormikProps<QuestionFormValues & Partial<BranchingFormValues>>;
}

type FinalFormValues =
  | QuestionFormValues
  | {
      branchingQuestion: BranchingFormValues | null;
    };

const AddQuestionModal: React.FC<Props> = ({
  isOpen,
  onClose,
  questionNumber = 1,
  initialValues,
  onSubmit,
  formik,
}) => {
  const { surveyId } = useSurveyUtil();
  const [currentStep, setCurrentStep] = useState(1);
  // const [firstStepValues, setFirstStepValues] =
  //   useState<QuestionFormValues | null>(null);
  const [showPreview, setShowPreview] = useState(false);
  const [previewConfig, setPreviewConfig] = useState<QuestionFormValues | null>(
    null
  );

  const [addQUestionToSurvey, { isLoading }] = useAddQUestionToSurveyMutation();

  const handleSaveAndClose = async (values: FinalFormValues) => {
    await addQUestionToSurvey({
      surveyId: surveyId,
      ...values,
    })
      .unwrap()
      .catch(() => {
        SwalMessage(null, "Question Creation Failed", "Ok", "error", false);
      });
    onClose();
  };

  // Modified to handle Preview and Save & Close separately
  const handleFirstStepSubmit = async (values: QuestionFormValues) => {
    // Default: Save & Close
    // if (values.allowBranching) {
    //   setFirstStepValues(values);
    //   // currentStep === 2 ? handleSaveAndClose(values) : setCurrentStep(2);
    //   setCurrentStep(2);
    // } else {
    //   handleSaveAndClose(values);
    // }
  };

  // New: handle Preview button click (validate before showing preview)
  const handlePreviewClick = async (
    values: QuestionFormValues,
    validateForm: any,
    setTouched: any
  ) => {
    const errors = await validateForm();
    if (Object.keys(errors).length === 0) {
      setPreviewConfig(values);
      setShowPreview(true);
    } else {
      // Mark all fields as touched to show Formik errors
      setTouched(
        Object.keys(errors).reduce((acc, key) => {
          acc[key] = true;
          return acc;
        }, {} as Record<string, boolean>)
      );
    }
  };

  const handleBranchingSubmit = (branchingValues: BranchingFormValues) => {
    // if (!firstStepValues) return;
    // const finalValues = {
    //   ...firstStepValues,
    //   ...branchingValues,
    // };
    // setPreviewConfig(finalValues);
    // setShowPreview(true);
    // onSubmit(finalValues);
  };

  const onOntoBranching = () => {
    formik.handleSubmit();
    formik.dirty && formik.isValid && setCurrentStep(2);
  };

  const handleBack = () => {
    setCurrentStep(1);
  };

  const handleSaveAndNew = async (values: FinalFormValues) => {
    await addQUestionToSurvey({
      surveyId: surveyId,
      ...values,
    });
    setCurrentStep(1);
    setShowPreview(false);
    setPreviewConfig(null);
    formik.resetForm(); // Reset form values to default
  };

  //* PREVIEW HANDLERS
  const handlePreviewBackAndEdit = () => {
    setShowPreview(false);
    setCurrentStep(1);
  };

  const handlePreviewSaveAndAdd = () => {
    if (previewConfig) {
      handleSaveAndNew(previewConfig);
    }
  };

  const handlePreviewSaveAndClose = () => {
    if (previewConfig) {
      handleSaveAndClose(previewConfig);
    }
  };

  if (showPreview && previewConfig) {
    return (
      <QuestionPreviewModal
        isOpen={true}
        onClose={onClose}
        questionConfig={previewConfig}
        onBackAndEdit={handlePreviewBackAndEdit}
        onSaveAndClose={handlePreviewSaveAndClose}
        onSaveAndNew={handlePreviewSaveAndAdd}
      />
    );
  }

  return (
    <Modal
      className="rx-right-modal modal-right"
      scrollable={true}
      show={isOpen}
      onHide={onClose}
    >
      <Modal.Header className="border-0 p-0">
        <Row className="align-items-baseline">
          <Col xs={10} className="mt-auto mb-auto">
            <h2 className="mb-0">
              {currentStep === 1 ? "Add Question" : "Branching Configuration"}
            </h2>
          </Col>
          <Col xs={2} className="text-end mb-3">
            <span className="close-btn cursor-pointer" onClick={onClose}>
              <RxCross2 fontSize={20} />
            </span>
          </Col>
        </Row>
        <Row className="mt-3">
          <Col>
            <h3>Question No.{questionNumber}</h3>
          </Col>
        </Row>
      </Modal.Header>

      {currentStep === 1 ? (
        <form className="h-100" onSubmit={formik.handleSubmit}>
          <Modal.Body
            className="p-0 mt-2"
            style={{
              height: "calc(100vh - 150px)",
              overflowY: "auto",
              paddingBottom: "80px",
            }}
          >
            <div>
              <div className="mb-3">
                <FormLabel>Question Title</FormLabel>
                <FormInput
                  name="questionText"
                  placeholder="Enter Question Title"
                />
              </div>
              <div className="mb-3">
                <FormLabel>Response Type</FormLabel>
                <ResponseTypeDropdown
                  name="responseType"
                  onChange={() => {
                    Object.keys(responseViewerFelid).forEach((key) => {
                      formik.setFieldValue(
                        key,
                        responseViewerFelid[
                          key as keyof typeof responseViewerFelid
                        ]
                      );
                    });
                  }}
                />
              </div>
              <div>
                <ResponseViewer responseType={formik.values.responseType} />
              </div>
              <h3 className="mt-5 mb-1">Settings</h3>
              <hr className="mb-5 mt-3" />
              <div className="d-flex flex-column gap-5">
                <div className="d-flex align-items-center justify-content-between">
                  <FormLabel required={false}>Required</FormLabel>
                  <FormSwitch name="isRequired" />
                </div>
                <div>
                  <div className="d-flex align-items-center justify-content-between">
                    <FormLabel required={false}>Allow Attachment</FormLabel>
                    <FormSwitch name="allowAttachment" />
                  </div>
                  {formik.values.allowAttachment && (
                    <div className="mb-3">
                      <AllowAttachmentType />
                    </div>
                  )}
                </div>
                <div className="d-flex align-items-center justify-content-between">
                  <FormLabel required={false}>Allow Comment</FormLabel>
                  <FormSwitch name="allowComment" />
                </div>
                <div className="d-flex align-items-center justify-content-between">
                  <FormLabel required={false}>General Ticket</FormLabel>
                  <FormSwitch name="autoTicketGeneration" />
                </div>

                {/* Auto Ticket Options Selector */}
                <AutoTicketOptionsSelector />

                <div className="d-flex align-items-center justify-content-between">
                  <FormLabel required={false}>Branching</FormLabel>
                  <FormSwitch name="allowBranching" />
                </div>
              </div>
            </div>
          </Modal.Body>
          <Modal.Footer
            className="border-0 p-3"
            style={{
              position: "absolute",
              bottom: 0,
              left: 0,
              right: 0,
              backgroundColor: "var(--Rx-bg)",
            }}
          >
            <div className="d-flex justify-content-between w-100">
              {formik.values.allowBranching ? (
                <div />
              ) : (
                <div className="">
                  <button
                    type="button"
                    className="btn btn-outline"
                    disabled={formik.isSubmitting}
                    onClick={() =>
                      handlePreviewClick(
                        formik.values,
                        formik.validateForm,
                        formik.setTouched
                      )
                    }
                  >
                    Preview
                  </button>
                  <button
                    type="button"
                    className="btn rx-btn ms-3"
                    disabled={formik.isSubmitting}
                    onClick={() => {
                      formik.handleSubmit();
                      handleSaveAndClose(formik.values);
                    }}
                  >
                    Save & Close
                  </button>
                </div>
              )}
              <div className="">
                {formik.values.allowBranching ? (
                  <button
                    type="button"
                    className="btn rx-btn"
                    // disabled={formik.isSubmitting}
                    onClick={onOntoBranching}
                  >
                    Add Branching Question
                  </button>
                ) : (
                  <button
                    type="button"
                    className="btn rx-btn"
                    disabled={formik.isSubmitting}
                    onClick={async () => {
                      formik.handleSubmit();
                      await handleSaveAndNew(formik.values);
                    }}
                  >
                    Save & Add New
                  </button>
                )}
              </div>
            </div>
          </Modal.Footer>
        </form>
      ) : (
        <div className="h-100">
          <Modal.Body
            className="p-0 mt-2"
            style={{
              height: "calc(100% - 0px)",
              overflowY: "auto",
              paddingBottom: "80px",
            }}
          >
            <BranchingSection
              onSubmit={() => {}}
              onSaveAndClose={async (values) => {
                await handleSaveAndClose({
                  ...formik.values,
                  branchingQuestion: values ? values : null,
                });
              }}
              onBack={() => setCurrentStep(1)}
              onSaveAndNew={async (values) => {
                await handleSaveAndNew({
                  ...formik.values,
                  branchingQuestion: values ? values : null,
                });
                formik.resetForm();
              }}
            />
          </Modal.Body>
        </div>
      )}
    </Modal>
  );
};

export default AddQuestionModal;
