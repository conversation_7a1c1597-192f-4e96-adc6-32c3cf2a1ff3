import { QuestionOptions } from "../../../../../apis/type";
import { QuestionType } from "../../../createSurvey/types";

export const enum ResponseType {
  MULTIPLE_CHOICE = "MULTIPLE_CHOICE",
  SCALE = "SCALE",
  YES_NO = "YES_NO",
  THUMBS_UP_DOWN = "THUMBS_UP_DOWN",
  COMMENT = "COMMENT",
  EMOJI = "EMOJI",
  RATING = "RATING",
  NET_PROMOTER_SCORE = "NET_PROMOTER_SCORE",
}

// Utility functions for generating options based on response type
export const generateScaleOptions = (startScale: number, endScale: number): QuestionOptions[] => {
  const options: QuestionOptions[] = [];
  for (let i = startScale; i <= endScale; i++) {
    options.push({
      optionText: i.toString(),
      optionValue: i.toString(),
      isTicketRequired: false,
    });
  }
  return options;
};

export const generateRatingOptions = (maxRating: number): QuestionOptions[] => {
  const options: QuestionOptions[] = [];
  for (let i = 1; i <= maxRating; i++) {
    options.push({
      optionText: i.toString(),
      optionValue: i.toString(),
      isTicketRequired: false,
    });
  }
  return options;
};

export const generateNpsOptions = (startNps: number = 0, endNps: number = 10): QuestionOptions[] => {
  const options: QuestionOptions[] = [];
  for (let i = startNps; i <= endNps; i++) {
    options.push({
      optionText: i.toString(),
      optionValue: i.toString(),
      isTicketRequired: false,
    });
  }
  return options;
};

export type AttachmentType = "IMAGE" | "VIDEO" | "AUDIO";

export interface QuestionFormValues {
  questionText: string;
  responseType: ResponseType;
  allowAttachment: boolean;
  attachmentType?: AttachmentType[];
  allowComment: boolean;
  autoTicketGeneration: boolean;
  allowBranching: boolean;
  isRequired: boolean;
  // Multiple Choice and other options
  options?: QuestionOptions[];
  // Scale - used by ScaleViewer component
  startScale?: number;
  endScale?: number;
  // Rating - used by RatingViewer component
  ratting?: number;
  rattingIcon?: string;
  // NPS - used by NpsViewer component
  startNetPromoterScore?: number;
  endNetPromoterScore?: number;
}

export const QUESTION_TYPE_OPTIONS = [
  { label: "Multiple Choice", value: "MULTIPLE_CHOICE" },
  { label: "Scale", value: "SCALE" },
  { label: "Yes / No", value: "YES_NO" },
  { label: "Thumbs Up/ Thumbs Down", value: "THUMBS_UP_DOWN" },
  { label: "Comment", value: "COMMENT" },
  { label: "Emoji", value: "EMOJI" },
  { label: "Rating", value: "RATING" },
  { label: "Net Promoter Score", value: "NET_PROMOTER_SCORE" },
];

export const emojiOptions = [
  {
    value: "slightly_smiling_face",
    icon: "🙂",
    label: "Slightly Smiling Face",
  },
  {
    value: "slightly_frowning_face",
    icon: "🙁",
    label: "Slightly Frowning Face",
  },
  { value: "neutral_face", icon: "😐", label: "Neutral Face" },
];

export const thumbsOptions = [
  { value: "thumb_up", label: "Thumb Up" },
  { value: "thumb_down", label: "Thumb Down" },
];

// export const mockQuestion: QuestionType[] = [
//   {
//     questionText: "Test",
//     responseType: ResponseType.MULTIPLE_CHOICE,
//     allowAttachment: true,
//     attachmentType: ["IMAGE", "VIDEO"],
//     allowComment: true,
//     autoTicketGeneration: true,
//     allowBranching: true,
//     isRequired: true,
//     options: ["Test", "TWat 33"],
//     startScale: 0,
//     endScale: 5,
//     ratting: 5,
//     rattingIcon: "star",
//     startNetPromoterScore: 0,
//     endNetPromoterScore: 10,
//     branchingQuestionText: "Twa",
//     allowbranchingAttchement: true,
//     branchingAttchementType: ["IMAGE"],
//   },
//   {
//     questionText: "Test",
//     responseType: ResponseType.SCALE,
//     allowAttachment: true,
//     attachmentType: ["IMAGE", "VIDEO"],
//     allowComment: true,
//     autoTicketGeneration: true,
//     allowBranching: true,
//     isRequired: true,
//     options: [""],
//     startScale: 0,
//     endScale: 5,
//     ratting: 5,
//     rattingIcon: "star",
//     startNetPromoterScore: 0,
//     endNetPromoterScore: 10,
//     branchingQuestionText: "12",
//     allowbranchingAttchement: false,
//     branchingAttchementType: [],
//   },
//   {
//     questionText: "Test",
//     responseType: ResponseType.YES_NO,
//     allowAttachment: false,
//     attachmentType: [],
//     allowComment: false,
//     autoTicketGeneration: false,
//     allowBranching: false,
//     isRequired: false,
//     options: ["Yes", "No"],
//     startScale: 0,
//     endScale: 5,
//     ratting: 5,
//     rattingIcon: "star",
//     startNetPromoterScore: 0,
//     endNetPromoterScore: 10,
//   },
//   {
//     questionText: "Test",
//     responseType: ResponseType.THUMBS_UP_DOWN,
//     allowAttachment: false,
//     attachmentType: [],
//     allowComment: false,
//     autoTicketGeneration: false,
//     allowBranching: false,
//     isRequired: false,
//     options: ["thumb_up", "thumb_down"],
//     startScale: 0,
//     endScale: 5,
//     ratting: 5,
//     rattingIcon: "star",
//     startNetPromoterScore: 0,
//     endNetPromoterScore: 10,
//   },
//   {
//     questionText: "Test",
//     responseType: ResponseType.COMMENT,
//     allowAttachment: false,
//     attachmentType: [],
//     allowComment: false,
//     autoTicketGeneration: false,
//     allowBranching: false,
//     isRequired: false,
//     options: [""],
//     startScale: 0,
//     endScale: 5,
//     ratting: 5,
//     rattingIcon: "star",
//     startNetPromoterScore: 0,
//     endNetPromoterScore: 10,
//   },
//   {
//     questionText: "Test",
//     responseType: ResponseType.EMOJI,
//     allowAttachment: false,
//     attachmentType: [],
//     allowComment: false,
//     autoTicketGeneration: false,
//     allowBranching: false,
//     isRequired: false,
//     options: [
//       "slightly_smiling_face",
//       "slightly_frowning_face",
//       "neutral_face",
//     ],
//     startScale: 0,
//     endScale: 5,
//     ratting: 5,
//     rattingIcon: "star",
//     startNetPromoterScore: 0,
//     endNetPromoterScore: 10,
//   },
//   {
//     questionText: "Test",
//     responseType: ResponseType.RATING,
//     allowAttachment: false,
//     attachmentType: [],
//     allowComment: false,
//     autoTicketGeneration: false,
//     allowBranching: false,
//     isRequired: false,
//     options: [""],
//     startScale: 0,
//     endScale: 5,
//     ratting: 5,
//     rattingIcon: "star",
//     startNetPromoterScore: 0,
//     endNetPromoterScore: 10,
//   },
//   {
//     questionText: "Test",
//     responseType: ResponseType.NET_PROMOTER_SCORE,
//     allowAttachment: false,
//     attachmentType: [],
//     allowComment: false,
//     autoTicketGeneration: false,
//     allowBranching: false,
//     isRequired: false,
//     options: [""],
//     startScale: 0,
//     endScale: 5,
//     ratting: 5,
//     rattingIcon: "star",
//     startNetPromoterScore: 0,
//     endNetPromoterScore: 10,
//   },
// ];
