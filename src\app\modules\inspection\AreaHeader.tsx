import { ArrayHelpers, Field, FieldProps, FormikContextType, useFormikContext } from "formik";
import { autoCapitalizeWithSpecialChar } from "../../utils/CommonUtils";
import React from "react";
import FormErrorMessage from "../../component/form/FormErrorMessage";
import FormLabel from "../../component/form/FormLabel";
import { MdOutlineDeleteForever } from "react-icons/md";
import { RxDragHandleHorizontal } from "react-icons/rx";
import { DraggableProvided } from "react-beautiful-dnd";
import AreaAttchmentInput from "./AreaAttchmentInput";

interface Props {
  fieldArray: ArrayHelpers;
  blockIndex: number;
  provided: DraggableProvided | null;
  length: number;
  viewOnly?: boolean;
}

const AreaHeader: React.FC<Props> = ({
  blockIndex,
  fieldArray,
  provided,
  length,
  viewOnly = false,
}) => {
  const { remove } = fieldArray;
  const { setFieldValue, errors, setErrors } = useFormikContext<any>();
  return (
    // <div>AreaHeader</div>
    <div className="w-100">
      <div className="d-flex align-items-start justify-content-between">
        <FormLabel>Area Name ({blockIndex + 1})</FormLabel>
        {!viewOnly && (
          <div className="d-flex align-items-center gap-2">
            {length > 1 && (
              <button
                type="button"
                className="btn btn-sm btn-icon delete-icon"
                onClick={() => remove(blockIndex)}
              >
                <MdOutlineDeleteForever className="fs-2" />
              </button>
            )}
            {provided && (
              <div {...provided.dragHandleProps}>
                <RxDragHandleHorizontal className="drag-icon" />
              </div>
            )}
          </div>
        )}
      </div>
      <Field
        name={`inspectionTemplateAreaRequestDtos[${blockIndex}].areaName`}
        placeholder="Area Name"
        className="form-control"
        disabled={viewOnly}
        readOnly={viewOnly}
      >
        {({ field }: FieldProps) => (
          <input
            {...field}
            placeholder="Area Name"
            className="form-control"
            disabled={viewOnly}
            readOnly={viewOnly}
            onChange={(e: any) => {
              const value = autoCapitalizeWithSpecialChar(e.target.value);
              setFieldValue(field.name, value);
              if (errors && errors[field.name]) {
                const newErrors = { ...errors };
                delete newErrors[field.name];
                setErrors(newErrors);
              }
            }}
          />
        )}
      </Field>
      <FormErrorMessage
        name={`inspectionTemplateAreaRequestDtos[${blockIndex}].areaName`}
      />
    </div>
  );
};

export default AreaHeader;
