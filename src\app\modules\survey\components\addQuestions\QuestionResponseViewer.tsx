import React, { useMemo, useState } from "react";
import {
  emojiOptions,
  QuestionFormValues,
  ResponseType,
  thumbsOptions,
} from "./util/constant";
import { BranchingFormValues } from "./hooks/useBranchingForm";
import QuestionResponseWrapper from "./QuestionResponseWrapper";
import MultipleChoiceResponse from "../response/MultipleChoiceResponse";
import ScaleResponse from "../response/ScaleResponse";
import YesNoResponse from "../response/YesNoResponse";
import ThumbsUpDownResponse from "../response/ThumbsUpDownResponse";
import EmojiResponse from "../response/EmojiResponse";
import RatingResponse from "../response/RatingResponse";
import NpsResponse from "../response/NpsResponse";
import CommentResponse from "../response/CommentResponse";
import { ISurveyQuestionListItem } from "../../../../apis/type";

interface Props {
  question: ISurveyQuestionListItem;
  readOnly?: boolean;
  questionNumber?: number;
}

const QuestionResponseViewer: React.FC<Props> = ({
  question,
  questionNumber,
  readOnly,
}) => {
  const options = useMemo(() => {
    return (question.options || []).map((opt) => ({
      label: typeof opt === 'string' ? opt : opt.optionText,
      value: typeof opt === 'string' ? opt : opt.optionValue,
    }));
  }, [question.options]);

  // Derive scale values from options for SCALE response type
  const scaleValues = useMemo(() => {
    if (question.responseType === ResponseType.SCALE && question.options && question.options.length > 0) {
      const numericValues = question.options
        .map(opt => typeof opt === 'string' ? parseInt(opt) : parseInt(opt.optionValue))
        .filter(val => !isNaN(val))
        .sort((a, b) => a - b);

      return {
        startScale: numericValues[0] || 0,
        endScale: numericValues[numericValues.length - 1] || 5
      };
    }
    return { startScale: question.startScale || 0, endScale: question.endScale || 5 };
  }, [question.options, question.responseType, question.startScale, question.endScale]);

  // Derive rating values from options for RATING response type
  const ratingValues = useMemo(() => {
    if (question.responseType === ResponseType.RATING && question.options && question.options.length > 0) {
      const numericValues = question.options
        .map(opt => typeof opt === 'string' ? parseInt(opt) : parseInt(opt.optionValue))
        .filter(val => !isNaN(val));

      return {
        ratting: Math.max(...numericValues) || 5
      };
    }
    return { ratting: question.ratting || 5 };
  }, [question.options, question.responseType, question.ratting]);

  // Derive NPS values from options for NET_PROMOTER_SCORE response type
  const npsValues = useMemo(() => {
    if (question.responseType === ResponseType.NET_PROMOTER_SCORE && question.options && question.options.length > 0) {
      const numericValues = question.options
        .map(opt => typeof opt === 'string' ? parseInt(opt) : parseInt(opt.optionValue))
        .filter(val => !isNaN(val))
        .sort((a, b) => a - b);

      return {
        startNetPromoterScore: numericValues[0] || 0,
        endNetPromoterScore: numericValues[numericValues.length - 1] || 10
      };
    }
    // Default NPS range if no options available
    return {
      startNetPromoterScore: 0,
      endNetPromoterScore: 10
    };
  }, [question.options, question.responseType]);

  // Local state for value
  const [responseValue, setResponseValue] = useState<string | number>("");

  const renderView = () => {
    switch (question.responseType) {
      case ResponseType.MULTIPLE_CHOICE:
        return (
          <MultipleChoiceResponse
            options={options}
            value={typeof responseValue === "string" ? responseValue : ""}
          />
        );
      case ResponseType.SCALE:
        return (
          <ScaleResponse
            startScale={scaleValues.startScale}
            endScale={scaleValues.endScale}
            value={
              typeof responseValue === "number" ? responseValue : undefined
            }
            onChange={setResponseValue}
          />
        );
      case ResponseType.YES_NO:
        return (
          <YesNoResponse
            options={options}
            value={typeof responseValue === "string" ? responseValue : ""}
            onChange={setResponseValue}
          />
        );
      case ResponseType.THUMBS_UP_DOWN:
        return (
          <ThumbsUpDownResponse
            options={thumbsOptions}
            value={typeof responseValue === "string" ? responseValue : ""}
            onChange={setResponseValue}
          />
        );
      case ResponseType.EMOJI:
        return (
          <EmojiResponse
            options={emojiOptions}
            value={typeof responseValue === "string" ? responseValue : ""}
            onChange={setResponseValue}
          />
        );
      case ResponseType.RATING:
        return (
          <RatingResponse
            ratting={ratingValues.ratting}
            rattingIcon={question.rattingIcon as "smiley" | "star" | "thumb_up"}
            value={typeof responseValue === "number" ? responseValue : 0}
            onChange={setResponseValue}
          />
        );
      case ResponseType.NET_PROMOTER_SCORE:
        return (
          <NpsResponse
            startNetPromoterScore={npsValues.startNetPromoterScore}
            endNetPromoterScore={npsValues.endNetPromoterScore}
            value={typeof responseValue === "number" ? responseValue : 0}
            onChange={setResponseValue}
          />
        );
      case ResponseType.COMMENT:
        return <CommentResponse />;
      default:
        return <></>;
    }
  };

  return (
    <QuestionResponseWrapper
      questionText={question.questionText}
      isRequired={question.isRequired}
      allowAttachment={question.allowAttachment}
      allowedAttachmentType={question.attachmentType}
      allowComment={question.allowComment}
      allowBranching={question.allowBranching}
      branchingQuestionText={question?.branchingQuestion?.questionText} // <-- updated property name
      allowbranchingAttchement={question.branchingQuestion?.allowAttachment}
      branchingAttchementType={question.branchingQuestion?.attachmentType}
      questionNumber={questionNumber}
    >
      {renderView()}
    </QuestionResponseWrapper>
  );
};

export default QuestionResponseViewer;