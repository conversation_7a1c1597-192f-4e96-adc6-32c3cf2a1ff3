import Swal from "sweetalert2";

const SwalMessage = (
  title: any,
  text: any,
  confirmButtonText: any,
  icon: any,
  isCancelButtonVisible: any
) => {
  // console.log(confirmButtonText);

  // Set the timer only if confirmButtonText is provided and isCancelButtonVisible is false
  const autoCloseAfter =
    confirmButtonText?.toLowerCase() === "ok" && !isCancelButtonVisible
      ? 5000
      : undefined;

  return new Promise(async (resolve) => {
    const result = await Swal.fire({
      title: title,
      text: text,
      icon: icon,
      iconColor: icon == "info" ? "#3085d6" : "",
      showCancelButton: isCancelButtonVisible,
      timer: autoCloseAfter, // Auto-close timer
      timerProgressBar: !!autoCloseAfter, // Show progress bar if timer is enabled
      allowEscapeKey: false,
      allowOutsideClick: false,
      confirmButtonText: confirmButtonText,
      customClass: {
        cancelButton: "order-1 rx-btn btn-black-lightblue",
        confirmButton: "order-2 rx-btn",
      },
      didOpen: (popup) => {
        // If only one button (no cancel), focus confirm so Enter works
        // if (!isCancelButtonVisible) {
        const confirmBtn = popup.querySelector(".swal2-confirm");
        if (confirmBtn && "focus" in confirmBtn)
          (confirmBtn as HTMLElement).focus();
        // }
      },
    });

    // Resolve based on user interaction or auto-dismiss
    if (
      result.isConfirmed || // User clicked confirm
      result.dismiss === Swal.DismissReason.timer // Auto-closed due to timer
    ) {
      resolve(true);
    } else {
      resolve(false);
    }
  });
};

export default SwalMessage;
