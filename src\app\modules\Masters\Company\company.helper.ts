import axios from "axios";
import { APIs } from "../../../serverconfig/apiURLs";
import axiosInstance from "../../../serverconfig/axiosInstance";
// import { BACKEND_BASE_URL } from "../../../serverconfig/constants";
import encryptDecryptUtil from "../../../utils/encrypt-decrypt-util";
const BACKEND_BASE_URL = import.meta.env.VITE_REACT_BACKEND_URL;

// const calculatePagination = (page: number,rowsPerPage:number,totalRows:number) => {
//     const offset: number = page * rowsPerPage;
//     const start: number = offset;
//     const length: number = Math.min(rowsPerPage, totalRows - offset);
//     return [start, length];
// };

class CompanyService {
  getGridData(
    page: any,
    size: any,
    search: any,
    sortOrder: any,
    columnName: any
  ) {
    // let startLength = calculatePagination(page,rowsPerPage,totalRows);
    // console.log(page, rowsPerPage, totalRows);
    let keyinfo = JSON.parse(localStorage.keyinfo);
    const bodyparam = {
      page: page,
      size: size,
      search: search ? search : "",
      sortingColumns: [
        {
          sortOrder: 0,
          columnName: "",
        },
      ],
    };
    const payload = encryptDecryptUtil.encryptData(
      JSON.stringify(bodyparam),
      keyinfo.syckey
    );

    return axiosInstance.post(APIs.GRIDCALLS.companyGrid, {
      encryptedData: payload,
    });
  }

  getEditData(id: any) {
    let keyinfo = JSON.parse(localStorage.keyinfo);
    const obj = { companyid: id };
    let payload = encryptDecryptUtil.encryptData(
      JSON.stringify(obj),
      keyinfo.syckey
    );
    return axiosInstance.post(APIs.ALL_HEADERS.editCompany, {
      encryptedData: payload,
    });
  }

  getCompanyPermission = () => {
    return axiosInstance.post(APIs.ALL_HEADERS.getCompanyPermission);
  };

  getDeleteData(id: any) {
    let keyinfo = JSON.parse(localStorage.keyinfo);
    const obj = { companyid: id };
    let payload = encryptDecryptUtil.encryptData(
      JSON.stringify(obj),
      keyinfo.syckey
    );
    return axiosInstance.post(APIs.ALL_HEADERS.deleteCompany, {
      encryptedData: payload,
    });
  }

  changeStatus(id: any) {
    let keyinfo = JSON.parse(localStorage.keyinfo);
    const obj = { companyid: id };
    let payload = encryptDecryptUtil.encryptData(
      JSON.stringify(obj),
      keyinfo.syckey
    );
    return axiosInstance.post(APIs.ALL_HEADERS.companyStatus, {
      encryptedData: payload,
    });
  }

  excelExport(search?: any) {
    return axiosInstance.get(APIs.ALL_HEADERS.companyExport, {
      params: { search: search ? search.trim() : "" },
      responseType: "blob",
    });
  }

  fetchImage(imagePath: String | null) {
    let keyinfo = JSON.parse(localStorage.keyinfo);
    return axios.get(BACKEND_BASE_URL + "resources/files/" + imagePath, {
      headers: {
        txnNumber: `${keyinfo.txnnumber}`,
        Authorization: `Bearer ${localStorage.token}`,
      },
      responseType: "blob",
    });
  }
  uploadDocument(payload: any, imagePath: any) {
    let keyinfo = JSON.parse(localStorage.keyinfo);
    const encPayload = encryptDecryptUtil.encryptData(
      JSON.stringify(payload),
      keyinfo.syckey
    );
    const formData = new FormData();
    formData.append("encryptedData", encPayload);
    formData.append("files", imagePath);
    return axiosInstance.post(APIs.MULTIPART.companyUploadDocument, formData);
  }

  // getCompanyPermission = () =>{
  //   return axiosInstance.post(APIs.ALL_HEADERS.getCompanyPermission)
  //  }

  validateCompanyCode(companyCode: any) {
    let keyinfo = JSON.parse(localStorage.keyinfo);
    const bodyparms = {
      companyCode: companyCode,
    };
    // console.log(bodyparms)
    let payload = encryptDecryptUtil.encryptData(
      JSON.stringify(bodyparms),
      keyinfo.syckey
    );
    return axiosInstance.post(APIs.ALL_HEADERS.validatecompanycode, {
      encryptedData: payload,
    });
  }

  deleteCompanyDocument(id: any, companyId: any) {
    let keyinfo = JSON.parse(localStorage.keyinfo);
    const obj = {
      documentId: id ? id : "",
      companyId: companyId ? companyId : ""
    }
    let payload = encryptDecryptUtil.encryptData(
      JSON.stringify(obj),
      keyinfo.syckey
    );
    return axiosInstance.post(APIs.ALL_HEADERS.deleteCompanyDocument, {
      encryptedData: payload,
    });
  }
}
export const companyService = new CompanyService();
