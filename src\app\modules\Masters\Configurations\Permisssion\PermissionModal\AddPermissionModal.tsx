import { Switch } from '@progress/kendo-react-inputs'
import { useEffect, useState } from 'react'
import { Col, Modal, Row } from 'react-bootstrap'
import { RxCross2 } from 'react-icons/rx'
import { AutoCapitalize } from '../../../../../utils/CommonUtils'
import encryptDecryptUtil from '../../../../../utils/encrypt-decrypt-util'
import SwalMessage from '../../../../common/SwalMessage'
import { permissionService } from '../Permission.helper'

const AddPermissionModal = ({ addPermissionModal, setAddPermissionModal, setGridLoading, getPermissionGridData, permissionId, setPermissionId, isEdit, setIsEdit }: any) => {
    const userInfo = JSON.parse(localStorage.getItem("userinfo") || "{}");
    const [permissionName, setPermissionName] = useState<string>("");
    const [moduleData, setModuleData] = useState<{ moduleId: string; actionData: { actionId: string; actionName: string }[] }[]>([]);
    const [switchStates, setSwitchStates] = useState<Record<string, boolean>>({});
    const [isFormValid, setIsFormValid] = useState(false);
    const [selectedComapany, setSelectedCompany] = useState<any>([]);
    const [editData, setEditData] = useState<any>(null);
    const [errors, setErrors] = useState<any>({});
    const [moduleSwitchStates, setModuleSwitchStates] = useState<Record<string, boolean>>({});
    const [isDefault, setIsDefault] = useState<boolean>(false)
    const [isActive, setIsActive] = useState<boolean>(false)
    const [loadingStates, setLoadingStates] = useState({
        moduleData: false,
        companiesData: false,
        editData: false
    });

    // Check if any API is loading
    const isAnyApiLoading = Object.values(loadingStates).some(loading => loading);

    // handle loader when api is proccess
    useEffect(() => {
        setGridLoading(isAnyApiLoading)
    }, [isAnyApiLoading])


    const getModuleData = async () => {
        setLoadingStates(prev => ({ ...prev, moduleData: true }));
        let keyinfo = JSON.parse(localStorage.keyinfo);
        try {
            const res = await permissionService.getmoduleandactiondropdown("");
            if (res?.data?.success) {
                const decrypted = JSON.parse(
                    encryptDecryptUtil.decryptData(res.data.data, keyinfo.syckey)
                );
                setModuleData(decrypted);
            }
        } catch (error: any) {
            SwalMessage(null, error.message, "ok", "error", false);
        } finally {
            setLoadingStates(prev => ({ ...prev, moduleData: false }));
        }
    };

    useEffect(() => {
        if (addPermissionModal) {
            getModuleData();
        }
    }, [addPermissionModal]);

    const getcompaniesforpermission = async () => {
        setLoadingStates(prev => ({ ...prev, companiesData: true }));
        let keyinfo = JSON.parse(localStorage.keyinfo);
        try {
            const res = await permissionService.getcompaniesforpermission();
            // if (res?.data?.success) {
            //     const decrypted = JSON.parse(
            //         encryptDecryptUtil.decryptData(res.data.data, keyinfo.syckey)
            //     );
            //     console.log("Companies for Permission", decrypted);
            //     setGridLoading(false)
            // }
        } catch (error) {
            SwalMessage(null, error, "Ok", "error", false);
        } finally {
            setLoadingStates(prev => ({ ...prev, companiesData: false }));
        }
    }
    useEffect(() => {
        if (addPermissionModal && userInfo?.displayusertype === "Super Admin") {
            getcompaniesforpermission();
        }
    }, [addPermissionModal])

    // edit api response
    const EditPermisssionData = async (permissionId: any) => {
        setLoadingStates(prev => ({ ...prev, editData: true }));
        let keyinfo = JSON.parse(localStorage.keyinfo);
        try {
            const res = await permissionService.getPermisssionEditData(permissionId);
            if (res?.data?.success) {
                const decrypted = JSON.parse(
                    encryptDecryptUtil.decryptData(res.data.data, keyinfo.syckey)
                );
                setIsDefault(decrypted?.isDefault == 1 ? true : false)
                setPermissionName(decrypted.permissionName);
                setIsActive(decrypted?.active == 1 ? true : false)
                // setSelectedAction(actionData.find((action: any) => action.id == decrypted.active));
                setSelectedCompany(decrypted.companies || []);
                setEditData(decrypted);
            }
        } catch (error: any) {
            SwalMessage(null, error.message, "Ok", "error", false);
        } finally {
            setLoadingStates(prev => ({ ...prev, editData: false }));
        }
    }
    useEffect(() => {
        if (isEdit && permissionId && addPermissionModal) {
            EditPermisssionData(permissionId);
        }

    }, [permissionId, addPermissionModal])

    useEffect(() => {
        if (editData && addPermissionModal && moduleData.length > 0) {
            const newModuleSwitches: any = {};
            const newActionSwitches: any = {};

            // First, process all the selected modules and actions from editData
            editData.moduleActionData.forEach((editModule: any, index: number) => {
                // Try different possible field names for moduleId
                const moduleId = editModule.module || editModule.moduleId || editModule.id;
                const actionData = editModule.actionData || editModule.actions || editModule.actionIds;

                // Enable module switch if it has any selected actions
                if (moduleId && actionData && actionData.length > 0) {
                    newModuleSwitches[moduleId] = true;

                    // Enable all corresponding action switches
                    actionData.forEach((action: any) => {
                        // Extract actionId from the action object
                        const actionId = typeof action === 'object' ? action.actionId : action;
                        const key = `${moduleId}_${actionId}`;
                        newActionSwitches[key] = true;
                    });
                }
            });
            // Set the states
            setModuleSwitchStates(newModuleSwitches);
            setSwitchStates(newActionSwitches);
        }
    }, [editData, moduleData, addPermissionModal]);

    //modal close state null
    useEffect(() => {
        if (!addPermissionModal) {
            setPermissionName("");
            setModuleData([]);
            setSwitchStates({});
            setSelectedCompany([]);
            setErrors({});
            setIsFormValid(false);
            setPermissionId("");
            setEditData(null);
            setModuleSwitchStates({});
            setIsDefault(false)
            setIsEdit(false)
            setIsActive(false)
        }
    }, [addPermissionModal]);

    //format modaule id 
    const getFormattedModules = () => {
        const modules: {
            module: string;
            actions: string[];
        }[] = [];

        moduleData.forEach((mod: any) => {
            const selectedActions = mod.actionData
                .filter((action: any) => switchStates[`${mod.moduleId}_${action.actionId}`])
                .map((action: any) => action.actionId);

            if (selectedActions.length > 0) {
                modules.push({
                    module: mod.moduleId,
                    actions: selectedActions,
                });
            }
        });
        return modules;
    };

    //form validation
    const validateForm = () => {
        const newErrors: any = {};
        if (permissionName.trim() === "") {
            newErrors.permissionName = "Permission Name is required";
        }
        if (userInfo?.displayusertype === "Super Admin" && selectedComapany.length === 0) {
            newErrors.selectedCompany = "At least one company must be selected";
        }
        if (getFormattedModules().length === 0) {
            newErrors.modules = "At least one module with action must be selected";
        }
        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    }

    // Handle form submission
    useEffect(() => {
        if (permissionName.trim() !== ""
            && (userInfo?.displayusertype === "Super Admin" ? selectedComapany.length > 0 : true) && getFormattedModules().length != 0
        ) {
            setIsFormValid(true);
        } else { setIsFormValid(false); }
    }, [permissionName, selectedComapany, moduleSwitchStates ,switchStates]);

    const handleSubmit = async () => {
        if (validateForm()) {
            setGridLoading(true);
            const formattedModules = getFormattedModules();
            const payload: any = {
                permissionId: permissionId ? permissionId : "",
                permissionName: permissionName ? permissionName : "",
                active: isActive == true ? 1 : 0,
                isAllSelected: 0,
                companyIds: userInfo?.displayusertype === "Super Admin" ? selectedComapany : [],
                modules: formattedModules ? formattedModules : [],
                isDefault: isDefault == true ? 1 : 0
            }
            await permissionService.createPermission(payload)
                .then((res: any) => {
                    if (res?.data?.success) {
                        SwalMessage(
                            null,
                            res?.data?.errormsg,
                            "Ok",
                            "success",
                            false
                        ).then((isConfirm) => {
                            if (isConfirm) {
                                setAddPermissionModal(false);
                                setGridLoading(true);
                                getPermissionGridData(1);
                            }
                        })
                    } else {
                        SwalMessage(null, res?.data?.errormsg, "Ok", "error", false);
                        setAddPermissionModal(false);
                    }
                }).catch((error: any) => {
                    SwalMessage(null, error.message, "Ok", "error", false);
                    setGridLoading(false);
                }).finally(() => {
                    setGridLoading(false);
                });
        }
    }
    const handleSwitchToggle = (uniqueKey: string, value: boolean) => {
        const [moduleId, actionId] = uniqueKey.split("_");

        // Check if this is the "User Management" module
        if (moduleId === "5953752b4664456f73714b586762787955774a6d6d513d3d") {
            const module = moduleData.find((mod: any) => mod.moduleId === moduleId);
            if (module && module.actionData.length > 0) {
                const defaultActionId = module.actionData[0].actionId;

                // Prevent turning off the default action
                if (actionId === defaultActionId && !value) {
                    return; // Block toggle
                }
            }
        }

        setSwitchStates((prevState) => ({
            ...prevState,
            [uniqueKey]: value,
        }));
    };
    const handleModuleSwitchToggle = (moduleId: string, value: boolean) => {
        setModuleSwitchStates((prev) => ({
            ...prev,
            [moduleId]: value,
        }));

        if (value) {
            // Set default action for "Manage User" when toggled ON
            if (moduleId === "5953752b4664456f73714b586762787955774a6d6d513d3d") {
                const module: any = moduleData.find((mod: any) => mod.moduleId === moduleId);
                if (module) {
                    const defaultAction = module.actionData[0];
                    if (defaultAction) {
                        const uniqueKey = `${moduleId}_${defaultAction.actionId}`;
                        setSwitchStates((prev) => ({
                            ...prev,
                            [uniqueKey]: true,
                        }));
                    }
                }
            }
        } else {
            // When module is turned OFF, remove all its action switches from switchStates
            setSwitchStates((prev) => {
                const updatedStates = { ...prev };
                const module = moduleData.find((mod: any) => mod.moduleId === moduleId);
                if (module) {
                    module?.actionData?.forEach((action: { actionId: string; actionName: string }) => {
                        const uniqueKey = `${moduleId}_${action.actionId}`;
                        delete updatedStates[uniqueKey];
                    });
                }
                return updatedStates;
            });
        }
    };


    return (
        <Modal className="modal-right" show={addPermissionModal} scrollable={true}>
            <Modal.Header className="border-0 p-0 mb-3">
                <Row className="align-items-baseline mb-3">
                    <Col xs={10}><h2 className="mb-0">{isEdit ? "Edit" : "Add"} Permission</h2></Col>
                    <Col xs={2} className="text-end mb-3">
                        <span className="cursor-pointer" onClick={() => setAddPermissionModal(false)}>
                            <RxCross2 fontSize={20} />
                        </span>
                    </Col>
                </Row>
            </Modal.Header>
            <Modal.Body className='p-4'>
                <Row>
                    <Col xs={12} className="mb-3 mobile-margin">
                        <span className="form-label mb-0">
                            Permission Name<span className="text-danger">*</span>
                        </span>
                        <input
                            type="text"
                            className="form-control"
                            placeholder="Enter Permission Name"
                            value={permissionName}
                            onChange={(event: any) => setPermissionName(AutoCapitalize(event.target.value))}
                        />
                        {errors?.permissionName && (
                            <div className="text-danger mt-1">
                                {errors.permissionName}
                            </div>
                        )}
                    </Col>
                    {errors?.modules && (
                        <div className="text-danger">
                            {errors.modules}
                        </div>
                    )}
                    <Col xs={12} className="mb-3 mobile-margin">
                        {moduleData.map((item: any, index: any) => (
                            <div key={item.moduleId} className={`mb-4`}>
                                {/* Module Header with Switch */}
                                <div className="d-flex justify-content-between align-items-center mb-2">
                                    <h3 className="fw-semibold mb-0">{item.moduleName}</h3>
                                    <Switch
                                        offLabel=""
                                        onLabel=""
                                        checked={moduleSwitchStates[item.moduleId] || false}
                                        onChange={(e) => handleModuleSwitchToggle(item.moduleId, e.value)}
                                        className="standard-switch"
                                    />
                                </div>
                                {/* Action Switches (Visible only if Module is Active) */}
                                {moduleSwitchStates[item.moduleId] && item.actionData.map((action: any, actionIndex: any) => {
                                    const uniqueKey = `${item.moduleId}_${action.actionId}`;
                                    const isLast = actionIndex === item.actionData.length - 1;
                                    return (
                                        <div
                                            key={uniqueKey}
                                            className={`d-flex justify-content-between align-items-center ps-4 py-2 border-bottom`}
                                        >
                                            <span className='fs-6'>{action.actionName}</span>
                                            <Switch
                                                offLabel=""
                                                onLabel=""
                                                checked={switchStates[uniqueKey] || false}
                                                onChange={(e) => handleSwitchToggle(uniqueKey, e.value)}
                                                className="standard-switch"
                                            />
                                        </div>
                                    );
                                })}
                            </div>
                        ))}
                    </Col>
                </Row>
            </Modal.Body>
            <Modal.Footer className="border-0 p-0 d-block">
                <Row className='align-items-center'>
                    <Col xs={`${isEdit ? 3 : 6}`} className="">
                        <input
                            type="checkbox"
                            className="form-check-input me-3 mt-1"
                            name="isdefault"
                            checked={isDefault}
                            onChange={(e) => setIsDefault(e.target.checked)}
                        />
                        <span className="fs-3" >
                            Default
                        </span>
                    </Col>
                    {
                        isEdit ? (<Col xs={3} className="">
                            <input
                                type="checkbox"
                                className="form-check-input me-3 mt-1"
                                name="isdefault"
                                checked={isActive}
                                onChange={(e) => setIsActive(e.target.checked)}
                            />
                            <span className="fs-3" >
                                Active
                            </span>
                        </Col>) : null
                    }

                    <Col xs={6} className='text-end'>
                        <button
                            className={`btn ${isFormValid ? "btn-success" : "rx-btn"}`}
                            onClick={handleSubmit}
                        // disabled={!isFormValid}
                        >
                            Save & Continue
                        </button>
                    </Col>
                </Row>
            </Modal.Footer>
        </Modal>
    )
}

export default AddPermissionModal
