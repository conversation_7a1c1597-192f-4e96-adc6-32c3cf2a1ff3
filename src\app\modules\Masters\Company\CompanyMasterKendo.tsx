import { orderBy } from "@progress/kendo-data-query";
import { GridColumn as Column, Grid } from "@progress/kendo-react-grid";
import { Tooltip } from "@progress/kendo-react-tooltip";
import { useEffect, useState } from "react";
import { FaFileDownload } from "react-icons/fa";
import { IoMdAdd, IoMdMore } from "react-icons/io";
import { Link, useNavigate } from "react-router-dom";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import "react-tooltip/dist/react-tooltip.css";
import Swal from "sweetalert2";
import {
  formatContactInputNumber,
  getImage,
  imageOnError,
  swalMessages,
} from "../../../utils/CommonUtils";
import encryptDecryptUtil from "../../../utils/encrypt-decrypt-util";
import Expand_Cell_Kendo_Data_Grid from "../../common/Expand_Cell_Kendo_Data_Grid";
import SwalMessage from "../../common/SwalMessage";
import Add_Company_Modal from "../../pages/Add_Company_Modal";
import Company_subKendo from "./Company_subKendo";
import { companyService } from "./company.helper";
import { useBreadcrumbContext } from "../../../../_metronic/layout/components/header/BreadcrumbsContext";
import { MdOutlineDeleteForever, MdOutlineEdit } from "react-icons/md";
import { Col, Row, Dropdown } from "react-bootstrap";
import Spinner from "../../common/Spinner";

function Company_master() {
  const initialSort: Array<any> = [{ field: "saudh", dir: "desc" }];
  const [sort, setSort] = useState(initialSort);
  const navigate = useNavigate();
  const [modal, setmodal] = useState(false);
  const [loading, setLoading] = useState<Boolean>(false);
  const [gridData, setGridData] = useState<any[]>([]);
  const [isLoopvendor, setIsLoopvendor] = useState(false);

  // Edit Data States
  const [companyEditData, setCompanyEditData] = useState("");
  const [isEdit, setIsEdit] = useState(false);
  const [companyid, setCompanyid] = useState("");

  const [isCompanyModal, setIsCompanyModal] = useState(false)
  // Pagination & Grid Utitlities
  const [search, setSearch] = useState("");
  const [perPage] = useState(10);
  const [totalRows, setTotalRows] = useState(0);
  const [totalCount, setTotalCount] = useState<any>();

  const initialDataState: any = { skip: 0, take: 10 };
  const [page, setPage] = useState<any>(initialDataState);
  const [pageSizeValue, setPageSizeValue] = useState<
    number | string | undefined
  >(initialDataState.take);
  const pageNumber = Math.floor(page.skip / page.take) + 1;

  const pageChange = (event: any) => {
    const { skip, take } = event.page;
    const targetEvent = event.targetEvent as any;
    const newTake = targetEvent.value == "All" ? totalCount : take;
    const newPageSizeValue = targetEvent.value == "All" ? "All" : take;

    setPage({ skip, take: newTake });
    setPageSizeValue(newPageSizeValue);
    // console.log("Page size value:", newPageSizeValue);
  };
  useEffect(() => {
    if (search) {
      setPage(initialDataState);
    }
  }, [search]);
  // Pagination End
  // Initial Data
  useEffect(() => {
    const pageNumber = Math.floor(page.skip / page.take) + 1;
    const timeoutId = setTimeout(() => {
      fetchData(pageNumber, page.take, search, null, null);
    }, 300);
    return () => clearTimeout(timeoutId);
  }, [search, page]);

  // Grid Function
  var fetchData = async (
    page: any,
    size: any,
    search: any,
    sortOrder: any,
    columnName: any
  ) => {
    let keyinfo = JSON.parse(localStorage.keyinfo);
    try {
      setLoading(true);
      companyService
        .getGridData(page, size, search, sortOrder, columnName)
        .then((response: any) => {
          if (response.status == 200) {
            const responseData = response.data;
            // console.log("response", response);
            if (responseData.success == true) {
              const decryptedData = encryptDecryptUtil.decryptData(
                responseData.data,
                keyinfo.syckey
              );
              const resultData = JSON.parse(decryptedData);
              setLoading(false);
              setGridData(resultData.data);
              setTotalCount(resultData.totalCount);
              setTotalRows(resultData.recordsTotal);
            } else {
              setLoading(false);
              SwalMessage(null, responseData?.errormsg, "Ok", "error", false);
            }
          }
        })
        .catch((e: any) => {
          if (e?.response?.status == 401) {
            // localStorage.removeItem("islogin");
            navigate("/admindashboard");
            // navigate(0);
          }
          setLoading(false);
          SwalMessage(null, e?.message, "Ok", "error", false);
        })
        .finally(() => {
          setLoading(false);
        });
      // const arrayofobj = encryptDecryptUtil.decryptData(
      //   response.data,
      //   keyinfo.syckey
      // );
      // const wholeData = JSON.parse(arrayofobj);
      // console.log("wholeData",wholeData);

      // setGridData(wholeData.data);
      // setTotalCount(wholeData.totalCount);
      // setTotalRows(wholeData.recordsTotal);
    } catch (error) {
      console.error("Error fetching data:", error);
    }
  };

  // change status
  const changeStatus = async (id: any, data: any) => {
    const confirmed = await SwalMessage(
      `Are You Sure ?`,
      `Do you really want to change the status of ${data?.companyname} company to ${data?.status == "Active" ? "Inactive" : "Active"} ?`,
      swalMessages.confirmButtonText.change,
      swalMessages.icon.info,
      true
    );
    if (confirmed) {
      try {
        setLoading(true);
        const response = await companyService
          .changeStatus(id)
          .then((response: any) => {
            setLoading(false);
            return response.data;
          })
          .catch((e: any) => {
            console.info(e);
            if (e.response.status == 401) {
              // localStorage.removeItem("islogin");
              navigate("/admindashboard");
              // navigate(0);
            }
            SwalMessage(null, e.message, "Ok", "error", false);
          });

        if (response.success == true) {
          SwalMessage(
            null,
            response.errormsg,
            "Ok",
            "success",
            false
          )
            // Swal.fire({
            //   title: "Success",
            //   text: response.errormsg,
            //   icon: "success",
            //   confirmButtonColor: "#3085d6",
            // })
            .then(() => {
              // setPage(initialDataState);
              fetchData(pageNumber, page.take, search, null, null);
            });
        }
      } catch (error) {
        console.error("Error fetching data:", error);
        SwalMessage(null, "Something Went Wrong.", "Ok", "error", false);
      }
    }
  };

  // xlsx export
  const excelexport = async () => {
    setLoading(true);
    try {
      const date = new Date();
      await companyService
        .excelExport(search)
        .then((response) => {
          const url = window.URL.createObjectURL(new Blob([response.data]));
          const link = document.createElement("a");
          link.href = url;
          link.setAttribute(
            "download",
            `Company_${date.getFullYear() + "_" + date.getMonth() + "_" + date.getDate()
            }.xlsx`
          );
          document.body.appendChild(link);
          link.click();
          window.URL.revokeObjectURL(url);
        })
        .catch((e: any) => {
          console.info(e);
          if (e.response.status == 401) {
            // localStorage.removeItem("islogin");
            navigate("/admindashboard");
            // navigate(0);
          }
          SwalMessage(null, e.message, "Ok", "error", false);
        });
    } catch (error) {
      console.error("Error downloading data:", error);
    } finally {
      setLoading(false);
    }
  };

  // Delete Function
  const handleDelete = async (id: any) => {
    const confirmed = await SwalMessage(
      swalMessages.title.commonTitle,
      swalMessages.text.deleteCompanyMsg,
      swalMessages.confirmButtonText.delete,
      swalMessages.icon.error,
      true
    );
    if (confirmed) {
      try {
        setLoading(true);
        const response = await companyService
          .getDeleteData(id)
          .then((response: any) => {
            setLoading(false);
            return response.data;
          })
          .catch((e: any) => {
            console.info(e);
            if (e.response.status == 401) {
              // localStorage.removeItem("islogin");
              navigate("/admindashboard");
              // navigate(0);
            }
            SwalMessage(null, e.message, "Ok", "error", false);
          });
        if (response.success == true) {
          SwalMessage(
            null,
            response.errormsg,
            "Ok",
            "success",
            false
          ).then((isConfirm) => {
            if (isConfirm) {
              fetchData(1, page.take, search, null, null);
            }
          });
        } else {
          SwalMessage(null, response.errormsg, "Ok", "error", false);
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    }
  };

  // Edit Function
  const handleedit = async (id: any) => {
    try {
      // console.log(id);
      setLoading(true);
      let keyinfo = JSON.parse(localStorage.keyinfo);
      const response = await companyService
        .getEditData(id)
        .then((response: any) => {
          setLoading(false);
          return response.data;
        })
        .catch((e: any) => {
          console.info(e);
          if (e.response.status == 401) {
            // localStorage.removeItem("islogin");
            navigate("/admindashboard");
            // navigate(0);
          }
          SwalMessage(null, e.message, "Ok", "error", false);
        });

      if (response.success == true) {
        setmodal(true);
        setIsLoopvendor(false);
        setCompanyEditData(
          JSON.parse(
            encryptDecryptUtil.decryptData(response.data, keyinfo.syckey)
          )
        );
        setCompanyid(id);
        setIsEdit(true);
      } else {
        SwalMessage(null, response.errormsg, "Ok", "error", false);
      }
    } catch (error) {
      console.error("Error fetching data:", error);
      SwalMessage(null, "Something Went Wrong.", "Ok", "error", false);
    }
  };

  const onItemChange = (event: any) => {
    if (event.field === "expanded_custom") {
      const newData = gridData.map((item: any) => {
        if (item.id === event.dataItem.id) {
          item.expanded = !event.dataItem.expanded;
        }
        return item;
      });
      setGridData(newData);
    }
  };

  // start switch
  const renderswitch = (id: any, data: any) => {
    return (
      <td>
        <div className="d-flex align-items-center">
          <div className="me-3">
            <input
              id={id}
              type="checkbox"
              className="checkbox"
              onChange={() => changeStatus(id, data)}
              checked={data?.status == "Active" ? false : true}
            />
            <label htmlFor={id} className="switch">
              <span className="switch__circle">
                <span className="switch__circle-inner"></span>
              </span>
              <span className="switch__left">Active</span>
              <span className="switch__right">Inactive</span>
            </label>
          </div>
        </div>
      </td>
    );
  };
  // end switch

  // start action
  const renderaction = (id: any) => {
    return (
      <td>
        {/* <div className="mx-2 text-center menu-hover">
          <a
            className="text-center"
            data-kt-menu-trigger="hover"
            data-kt-menu-placement="bottom-end"
          >
            <IoMdMore className="td-icon cursor-pointer" />
          </a>
          <div
            className="menu menu-sub menu-sub-dropdown w-100px w-md-100px mt-1"
            data-kt-menu="true"
          >
            <div className="menu-item">
              <a className="menu-link ">
                <span className="menu-title " onClick={() => handleedit(id)}>
                  <MdOutlineEdit className="me-4" />
                  Edit
                </span>
              </a>
              <a className="menu-link ">
                <span className="menu-title" onClick={() => handleDelete(id)}>
                  <MdOutlineDeleteForever className="me-4" />
                  Delete
                </span>
              </a>
            </div>
          </div>
        </div> */}
        <Dropdown className="new-chat-btn" style={{ position: 'static' }}>
          <Dropdown.Toggle
            as="span"
            className="fs-1 cursor-pointer ms-2"
          >
            <IoMdMore className="td-icon cursor-pointer" />
          </Dropdown.Toggle>
          <Dropdown.Menu align="end">
            <Dropdown.Item onClick={() => handleedit(id)}>
              <span className="fs-5" >
                <MdOutlineEdit className="me-4" size={18} />
                Edit
              </span>
            </Dropdown.Item>
            <Dropdown.Item onClick={() => handleDelete(id)}>
              <span className="fs-5" >
                <MdOutlineDeleteForever className="me-4" size={18} />
                Delete
              </span>
            </Dropdown.Item>
            {/* <Dropdown.Item
              onClick={() => handleStartGroupCreation("loto")}
            >
              <img
                src={lotoIcon}
                alt="icon"
                height={"18px"}
                width={"18px"}
                className="me-3"
              />{" "}
              Loto
            </Dropdown.Item> */}
          </Dropdown.Menu>
        </Dropdown>
      </td>
    );
  };
  // end action

  // start image
  const renderimage = (image: any) => {
    return (
      <td>
        <img
          src={getImage(image)}
          style={{
            width: "35px",
            height: "35px",
            border: "3px solid rgb(0 0 0 / 8%)",
          }}
          onError={imageOnError}
          className="user-image"
        ></img>
      </td>
    );
  };
  // end image

  const renderAccessType = (props: any) => {
    const { dataItem, field, content } = props;
    return (
      <td className="text-center">
        <span
          className={`badge fs-12px ${dataItem?.accessType == "Full" ? "badge-success px-5" : "badge-danger"
            }`}>
          {dataItem?.accessType}
        </span>
      </td>
    );
  };
  const renderTooltipCell = (props: any) => {
    const { dataItem, field, content } = props;
    return (
      <td
        className="k-table-td cursor-pointer"
        onClick={() => handleedit(dataItem.id)}
      >
        <span className="ellipsis-cell" title={content}>
          {dataItem[field]}
        </span>
      </td>
    );
  };

  const rendercontactperson = (props: any) => {
    const { dataItem, field, content } = props;
    return (
      <>
        <td
          className="k-table-td cursor-pointer"
          onClick={() => handleedit(dataItem.id)}
        >
          <svg
            style={{ height: "14px", width: "14px", marginRight: "2px" }}
            className="td-icon"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 448 512"
          >
            <path d="M313.6 304c-28.7 0-42.5 16-89.6 16-47.1 0-60.8-16-89.6-16C60.2 304 0 364.2 0 438.4V464c0 26.5 21.5 48 48 48h352c26.5 0 48-21.5 48-48v-25.6c0-74.2-60.2-134.4-134.4-134.4zM400 464H48v-25.6c0-47.6 38.8-86.4 86.4-86.4 14.6 0 38.3 16 89.6 16 51.7 0 74.9-16 89.6-16 47.6 0 86.4 38.8 86.4 86.4V464zM224 288c79.5 0 144-64.5 144-144S303.5 0 224 0 80 64.5 80 144s64.5 144 144 144zm0-240c52.9 0 96 43.1 96 96s-43.1 96-96 96-96-43.1-96-96 43.1-96 96-96z" />
          </svg>
          <span title={content}>{dataItem[field]}</span>
        </td>
      </>
    );
  };

  const renderemailicon = (props: any) => {
    const { dataItem, field, content } = props;
    return (
      <>
        <td
          className="k-table-td cursor-pointer"
          onClick={() => handleedit(dataItem.id)}
        >
          <svg
            style={{ height: "14px", width: "14px", marginRight: "3px" }}
            className="td-icon"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 512 512"
          >
            <path d="M464 64H48C21.5 64 0 85.5 0 112v288c0 26.5 21.5 48 48 48h416c26.5 0 48-21.5 48-48V112c0-26.5-21.5-48-48-48zm0 48v40.8c-22.4 18.3-58.2 46.7-134.6 106.5-16.8 13.2-50.2 45.1-73.4 44.7-23.2 .4-56.6-31.5-73.4-44.7C106.2 199.5 70.4 171.1 48 152.8V112h416zM48 400V214.4c22.9 18.3 55.4 43.9 104.9 82.6 21.9 17.2 60.1 55.2 103.1 55 42.7 .2 80.5-37.2 103.1-54.9 49.5-38.8 82-64.4 104.9-82.7V400H48z" />
          </svg>
          <span title={content}>{dataItem[field]}</span>
        </td>
      </>
    );
  };

  const renderphoneicon = (props: any) => {
    const { dataItem, field } = props;
    const formattedPhoneNumber = formatContactInputNumber(dataItem[field]);
    return (
      <>
        <td
          className="k-table-td cursor-pointer"
          onClick={() => handleedit(dataItem.id)}
        >
          <svg
            style={{ height: "14px", width: "14px", marginRight: "3px" }}
            className="td-icon"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 512 512"
          >
            <path d="M497.4 361.8l-112-48a24 24 0 0 0 -28 6.9l-49.6 60.6A370.7 370.7 0 0 1 130.6 204.1l60.6-49.6a23.9 23.9 0 0 0 6.9-28l-48-112A24.2 24.2 0 0 0 122.6 .6l-104 24A24 24 0 0 0 0 48c0 256.5 207.9 464 464 464a24 24 0 0 0 23.4-18.6l24-104a24.3 24.3 0 0 0 -14-27.6z" />
          </svg>
          <span title={formattedPhoneNumber}>{formattedPhoneNumber}</span>
          {/* {dataItem[field]} */}
        </td>
      </>
    );
  };

  const CustomIDCell = (props: { dataItem: any }) => {
    const { dataItem } = props;
    return (
      <td
        onClick={() => handleedit(dataItem.id)}
        className="k-table-td cursor-pointer"
      >
        {dataItem.viewid}
      </td>
    );
  };

  const addModal = () => {
    setmodal(true);
    setIsLoopvendor(false);
    setIsEdit(false);
    setCompanyEditData("");
  };

  // when add modal close at time search filed show empty
  useEffect(() => {
    if (!modal) {
      setSearch("")
    }
  }, [modal])

  const { setLabels } = useBreadcrumbContext();
  useEffect(() => {
    setLabels([{ path: "", state: {}, breadcrumb: "Company" }]);
  }, []);

  return (
    <>
      {loading && <Spinner />}
      <Row className="mb-7 pageheader">
        {/* <Col xl={5} lg={4} md={4} sm={4} className="mt-auto mb-auto">
          <h1 className="page-title mobile-margin mb-0">Companies</h1>
        </Col> */}
        <Col xl={3} lg={3} md={3} sm={12} className="mobile-margin">
          <input
            type="text"
            className="form-control "
            placeholder="Search..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            autoFocus
          />
        </Col>
        <Col xl={9} lg={9} md={9} sm={12} className="text-end">
          <span className="btn rx-btn me-4" onClick={excelexport}>
            <FaFileDownload className="me-1" />
            {""}
            Export
          </span>
          <span className="btn rx-btn" onClick={addModal}>
            <IoMdAdd className="me-2" />
            Add
          </span>
        </Col>
      </Row>
      <div className="card mt-0">
        <div className="card-body p-0">
          <div className="table_div">
            <Tooltip position="bottom" anchorElement="target">
              <Grid
                data={orderBy(gridData, sort)}
                skip={page.skip}
                take={page.take}
                total={totalCount}
                pageable={{
                  buttonCount: 4,
                  pageSizes: [10, 25, 50, 100, "All"],
                  pageSizeValue: pageSizeValue,
                }}
                onPageChange={pageChange}
                detail={Company_subKendo}
                sortable={true}
                sort={sort}
                onSortChange={(e: any) => {
                  setSort(e.sort);
                }}
                expandField="expanded"
                onItemChange={onItemChange}
              >
                <Column
                  field="expanded"
                  title=" "
                  width="46px"
                  cells={{ data: Expand_Cell_Kendo_Data_Grid }}
                />
                <Column
                  title=""
                  headerClassName="center-header"
                  cell={(props) =>
                    renderimage(props.dataItem.company_logo_light)
                  }
                  width="65px"
                  sortable={false}
                />
                <Column
                  field="viewid"
                  title="ID"
                  width="110px"
                  cell={CustomIDCell}
                />

                <Column
                  field="companyname"
                  title="Company Name"
                  cell={(props) =>
                    renderTooltipCell({
                      ...props,
                      content: props.dataItem.companyname,
                    })
                  }
                />
                <Column
                  field="companyaddress"
                  title="Address"
                  cell={(props) =>
                    renderTooltipCell({
                      ...props,
                      content: props.dataItem.companyaddress,
                    })
                  }
                />
                <Column
                  field="contactpersonname"
                  title="Contact Person"
                  cell={(props) =>
                    rendercontactperson({
                      ...props,
                      content: props.dataItem.contactpersonname,
                    })
                  }
                />
                <Column
                  field="companyemail"
                  title="Company Email"
                  cell={(props) =>
                    renderemailicon({
                      ...props,
                      content: props.dataItem.companyemail,
                    })
                  }
                />
                <Column
                  field="phonenumber"
                  title="Company Phone"
                  cell={(props) =>
                    renderphoneicon({
                      ...props,
                      content: props.dataItem.phonenumber,
                    })
                  }
                  width={"165px"}
                />
                <Column
                  title="Status"
                  width="100px"
                  headerClassName="center-header"
                  cell={(props) =>
                    renderswitch(props.dataItem.id, props.dataItem)
                  }
                />
                <Column
                  field="accessType"
                  title="Access Type"
                  headerClassName="center-header"
                  cell={(props) =>
                    renderAccessType({
                      ...props,
                      content: props.dataItem.accessType,
                    })
                  }
                />
                <Column
                  title="Action"
                  headerClassName="center-header"
                  width="80px"
                  cell={(props) => renderaction(props.dataItem.id)}
                  sortable={false}
                />
              </Grid>
            </Tooltip>
          </div>
        </div>
      </div>
      <Add_Company_Modal
        modal={modal}
        setmodal={setmodal}
        isLoopvendor={isLoopvendor}
        companyid={companyid}
        isEdit={isEdit}
        companyEditData={companyEditData}
        fetchData={fetchData}
        pageNumber={pageNumber}
        setIsCompanyModal={setIsCompanyModal}
        // loading={loading}
        setLoading={setLoading}
        setNewAssociateModal={false}
      />
      <ToastContainer
        position="top-right"
        autoClose={2000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover={false}
        theme="light"
      />
    </>
  );
}

export default Company_master;
