import { User } from "@progress/kendo-react-conversational-ui";
import {
  IDepartmentPayload,
  ILotoFormListPayload,
  ITicketListPayload,
  IUserDropDownPayload,
} from "../../apis/type";

export type NewConversionType = "ticket" | "dept" | "property" | "loto" | "inspection";
export type ListType = "ticket" | "dept" | "property" | "loto" | "inspection";
export type LoggedUserType = LoggedUserType;
export type ContentType = "TEXT" | "NOTIFICATION";

export type ChatMessageType = "THREAD_START" | "ROOM" | "THREAD";
export interface IChatCard {
  id: string;
  userImg: string;
  name: string;
  origin: "property" | "ticket" | "loto";
  text: string;
  time: string;
  mute: boolean;
  archive: boolean;
  badge?: string;
}

export interface IGroupList {
  PROPERTY_BASE: RoomsList[];
  TICKET_BASE: RoomsList[];
  LOTO_BASE: RoomsList[];
  INSPECTION_BASE: RoomsList[];
  archive: RoomsList[];
  DEPARTMENT_BASE: RoomsList[];
  LOTO_CONVERSION: RoomsList[];
}

export interface Author extends User {
  // id: string;
  // name: string;
  // avatarUrl: string;
  // avatarAltText: string;
}

export interface Attachment {
  file: File;
  type: string;
}

export interface Reactions {
  like?: number;
  dislike?: number;
  question?: number;
  exclamation?: number;
}

export type ICurrentUserType = "COMPANY_ADMIN" | "ADMIN" | "USER";

export type ICurrentSection =
  | "chatList"
  | "search"
  | "propertyList"
  | "ticketList"
  | "lotoList"
  | "inspectionList"
  | "userList"
  | "chat"
  | "conversionInfo"
  | "archiveChatList"
  | "departmentList";

export interface IGroupStepPayload {
  ticket?: Omit<ITicketListPayload, "page" | "size", "search">;
  dept?: Omit<IDepartmentPayload, "page" | "size" | "search">;
  user?:
    | IUserDropDownPayload
    | { lotoId?: string; roomId?: number; lotoFlag?: number; inspectionId?: string };
  // loto?: Omit<ILotoFormListPayload, "page" | "size" | "search">;
  loto?: IEquipmentidsforchatPayload;
  inspection?: {
    properties?: string[];
    departments?: string[];
  };
  roomId?: number;
}

//* ------ START SOCKET EVENT ------

export type RoomChatType =
  | "PROPERTY_BASE"
  | "TICKET_BASE"
  | "LOTO_BASE"
  | "DEPARTMENT_BASE"
  | "INSPECTION_BASE"
  | 'LOTO_CONVERSION'

export type ChatType = "ROOM";

export type ConnectionStatusType =
  | "initial"
  | "connected"
  | "pending"
  | "failed";

export type AttachmentType = "IMAGE" | "VIDEO" | "AUDIO";  
interface Attachment {
  id: string;
  type: AttachmentType
  url: string;
}

export interface CreateChatRoomPayload {
  chatAttachments: Attachment[];
  sender: Partial<Sender> | Record<string, never>;
  ticketId: string;
  roomName: string;
  members: string[];
  chatType: ChatType;
  userToken: string;
  deviceUniqueId: string;
  roomChatType: RoomChatType;
  propertyId?: string | null;
  departmentId?: string | null;
  lotoFormId?: string;
  inspectionId?: string;
  equipmentId?: string;
  typeFlag?: number;
  companyId?: number;
  inspectionName?: string;
}

export interface Sender {
  id: number;
  userToken: string;
  userName: string;
  displayName: string;
  firstName: string;
  lastName: string;
  profilePicUrl: string;
  emailAddress: string;
  online: boolean | number;
  lastSeen: number;
}

export interface RoomsList {
  unreadMessageCount: number;
  roomId: number;
  roomName: string;
  roomProfileUrl: string;
  roomMembersCount: number;
  roomCreatorId: string;
  chatType: ChatType;
  currentUserType: ICurrentUserType;
  roomCreatedTime: number;
  isArchived: boolean;
  isUserLeft: boolean;
  mute: boolean;
  propertyName?: string;
  roomChatType?: RoomChatType;
  lastMessageSentTime?: number;
  entireLastMessage?: EntireLastMessage;
  ticketId?: number;
  visible?: boolean;
}

export interface EntireLastMessage {
  userToken: string;
  message: string;
  clientMessageId: string;
  contentType: ContentType;
  chatAttachments: Attachment[];
  sender: Sender;
  timestamp: number;
  isSystemMessage: boolean;
  messageStatus: boolean;
}

export interface SendMessagePayload {
  chatAttachments: Attachment[];
  sender: Partial<Sender>;
  message: string;
  roomName: string;
  userToken: string;
  contentType: ContentType;
  roomId: number;
  isNotificationActive: boolean;
  clientMessageId: string;
  recipients: any;
  chatType: ChatType;
  parentMessageId: string;
  timestamp: number;
  deviceUniqueId: string;
}

export interface ChatHistoryPayload {
  userToken: string;
  roomId: number;
  size: number;
  page: number;
  deviceUniqueId: string;
}

export interface Message {
  id: string;
  author: Author;
  text: string;
  attachments?: Attachment[];
  replyingTo?: ChatHistoryItem;
}

export interface Parent {
  id: number;
  userToken: string;
  timestamp: number;
  chat: Chat | null;
  sender: Sender;
  messageReceipientType: string;
  chatMessageStatuses: ChatMessageStatuses[];
  message: string | undefined;
  dataChatMessageReactions: any[];
  dataMessageContentType: string;
  chatAttachments: Attachment[];
  parent: Parent | null;
  isSystemMessage: boolean;
  clientMessageId: string;
  messageStatus: boolean;
  roomName: string;
  chatMessageOriginator: ChatMessageOriginator2;
  systemMessage: boolean;
}

export interface Chat {
  id: number;
  chatType: string;
  createdOn: number;
  lastModifiedOn: number;
  lastMessageTimestamp: number;
  archived: boolean;
  name: string;
  creator: Creator;
  members: Member[];
  status: string;
  messageOriginatorType: string;
}

export interface Creator {
  id: number;
  userToken: string;
  userName: string;
  displayName: string;
  firstName: string;
  lastName: string;
  profilePicUrl: string;
  emailAddress: string;
  online: number;
  lastSeen: number;
}

export interface Member {
  id: number;
  chatUser: ChatUser;
  type: string;
  active: boolean;
  joinedOn: number;
  accessToHistoryFrom: number;
  isArchived: boolean;
  mute: boolean;
  user: ChatMessageUser;
}

export interface ChatUser {
  id: number;
  userToken: string;
  userName: string;
  displayName: string;
  firstName: string;
  lastName: string;
  emailAddress: string;
  online: number;
  lastSeen: number;
  profilePicUrl?: string;
}

export interface ChatMessageUser {
  id: number;
  userToken: string;
  userName: string;
  displayName: string;
  firstName: string;
  lastName: string;
  emailAddress: string;
  online: number;
  lastSeen: number;
  profilePicUrl?: string;
}

export interface Sender {
  id: number;
  userToken: string;
  userName: string;
  displayName: string;
  firstName: string;
  lastName: string;
  profilePicUrl: string;
  emailAddress: string;
  online: number;
  lastSeen: number;
}

export interface ChatMessageStatuses {
  id: number;
  user: ChatMessageUser;
  delivered: boolean;
  deliveredOn: number;
  read?: boolean;
  readOn?: number;
}

export interface ChatMessageOriginator {
  id: number;
  chatType: string;
  createdOn: number;
  lastModifiedOn: number;
  lastMessageTimestamp: number;
  archived: boolean;
  name: string;
  creator: Creator;
  members: Member[];
  status: string;
  messageOriginatorType: string;
}

export interface ChatHistoryItem {
  threadClientMessageId: string;
  chatMessageType: ChatMessageType;
  chatAttachments: Attachment[];
  userToken: string;
  message: string;
  clientMessageId: string;
  contentType: ContentType;
  sender: Partial<Sender>;
  timestamp: number;
  isSystemMessage: boolean;
  messageStatus: boolean;
  messageEdited: boolean;
  roomId: number;
  roomName: string;
  chatType: ChatType;
  isNotificationActive: boolean;
  reactions?: ReactionData[];
  parent?: Parent | null;
}

export interface ReactionData {
  messageId: number;
  reactionCount: number;
  reaction: ReactionType;
  isCurrentReaction: boolean;
}

export interface ReactionResponse {
  chatMessageType: ChatMessageType;
  clientMessageId: string;
  roomId: number;
  responseDTOS: ReactionData[];
  currentUserReaction: string;
}

export type ReactionType = "LIKE" | "DISLIKE" | "QUESTION" | "EXCLAMATORY";

export interface ReactionPayload {
  reaction: ReactionType;
  clientMessageId: string;
  timestamp: number;
  userToken: string;
  roomId: number;
  deviceUniqueId: string;
}

export interface ChatMessageDeletePayload {
  clientMessageId: string;
  userToken: string;
  roomId: number;
  messageStatus: boolean;
  deviceUniqueId: string;
}

export interface GroupInfoUpdateRoomPayload {
  roomName: string;
  roomProfileUrl: string;
  roomId: number;
}

export interface GroupInfoResponse {
  unreadMessageCount: number;
  entireLastMessage: EntireLastMessage;
  roomId: number;
  roomName: string;
  roomMembersCount: number;
  roomCreatorId: string;
  chatType: string;
  currentUserType: ICurrentUserType;
  roomCreatedTime: number;
  isArchived: boolean;
  isUserLeft: boolean;
  mute: boolean;
}

export interface EntireLastMessage {
  userToken: string;
  message: string;
  clientMessageId: string;
  contentType: ContentType;
  chatAttachments: Attachment[];
  sender: Sender;
  timestamp: number;
  isSystemMessage: boolean;
  messageStatus: boolean;
}

export interface LeaveRoomPayload {
  members: string[];
  roomId: number;
  userToken: string;
  deviceUniqueId: string;
}

export interface LeaveRoomSuccess {
  messageDTO: MessageDto;
  roomMemberDetailDTO: RoomMemberDetailDto;
}

export interface DeleteRoomPayload {
  roomId: number;
  userToken: string;
  deviceUniqueId: string;
}

export interface DeleteRoomResponse {
  roomId: number;
  roomName: string;
  acknowledgementStatus: string;
}

export interface MessageDto {
  userToken: string;
  message: string;
  sender: Sender;
  timestamp: number;
  isSystemMessage: boolean;
  messageStatus: boolean;
  roomId: number;
  roomName: string;
  chatType: ChatType;
  isNotificationActive: boolean;
}

export interface RoomMemberDetailDto {
  id: number;
  firstName: string;
  lastName: string;
  emailAddress: string;
  displayName: string;
  userName: string;
  userToken: string;
  profilePicUrl: string;
  type: string;
  roomId: number;
}

export interface ArchivePayload {
  roomId: number;
  userToken: string;
  deviceUniqueId: string;
}

export interface UserDeleteRoom {
  roomId: number;
  userToken: string;
  deviceUniqueId: string;
}

export interface EditMessagePayload {
  message: string;
  clientMessageId: string;
  roomId: number;
  deviceUniqueId: string;
}

export interface EditMessage {
  clientMessageId: string;
  roomId: number;
  message: string;
  chatMessageType: ChatMessageType;
}

export interface DeleteMessageAck {
  acknowledgementStatus: "MESSAGE_DELETED";
  messageId: string;
  roomId: number;
  chatMessageType: ChatMessageType;
}

export interface DeleteMessage {
  clientMessageId: string;
  chatMessageType: ChatMessageType;
  roomId: number;
}

export interface JoinRoomPayload {
  chatAttachments: Attachment[];
  sender: Partial<Sender> | Record<string, never>;
  members: string[];
  userToken: string;
  deviceUniqueId: string;
  roomId: number;
}

export interface MakeAdminPayload {
  members: string[];
  roomId: number;
  userToken: string;
  deviceUniqueId: string;
}

export interface TypingPayload {
  userToken: string;
  roomId: number;
  deviceUniqueId: string;
}
export interface TypingResponse {
  roomId: number;
  acknowledgementStatus: string;
}

export interface OnTypingResponse {
  userName: string;
  roomId: number;
  message: string;
  sender: Sender;
}

export interface RemoveUserPayload {
  chatAttachments: Attachment[];
  sender: Sender;
  members: string[];
  roomId: number;
  userToken: string;
  deviceUniqueId: string;
}

export interface RemoveUserResponse {
  messageDTO: RemoveUserMessageDto;
  roomMemberDetailDTO: RemoveUserRoomMemberDetailDto;
}

export interface RemoveUserMessageDto {
  userToken: string;
  message: string;
  sender: RemoveUserSender;
  timestamp: number;
  isSystemMessage: boolean;
  messageStatus: boolean;
  roomId: number;
  roomName: string;
  chatType: string;
  isNotificationActive: boolean;
}

export interface RemoveUserSender {
  id: number;
  userToken: string;
  userName: string;
  displayName: string;
  firstName: string;
  lastName: string;
  profilePicUrl: string;
  emailAddress: string;
  online: boolean;
  lastSeen: number;
}

export interface RemoveUserRoomMemberDetailDto {
  id: number;
  firstName: string;
  lastName: string;
  emailAddress: string;
  displayName: string;
  userName: string;
  userToken: string;
  profilePicUrl: string;
  type: string;
  roomId: number;
}

export interface ThreadMessage {
  acknowledgementStatus: "THREAD_MESSAGE_SENT";
  messageId: string;
  roomId: number;
  roomName: string;
  threadMessageId: string;
}

export interface ChatThreadHistoryItem extends ChatHistoryItem {}

export interface ChatThreadHistoryPayload extends ChatHistoryPayload {
  clientMessageId: string;
}

export interface SendThreadMessagePayload extends SendMessagePayload {
  threadStart: boolean;
  threadClientMessageId: string;
}

export interface ReadMessagePayload {
  clientMessageId: string;
  userToken: string;
  deviceUniqId: string;
}

export interface ReadMessageResponse {
  roomId: number;
  roomName: string;
  messageId: string;
  acknowledgementStatus: string;
}

export interface OnReadMessage {
  roomId: number;
  clientMessageId: string;
  userName: string;
}

export interface TickeDetailsPageChat {
  propertyId: string;
  departmentId: string;
  ticketId: string;
  ticketNumber: string;
  roomId: number;
}

export interface MuteRoomPayload {
  roomId: number;
  userToken: string;
  deviceUniqId: string;
  mute: boolean;
}

export interface MuteRoomResponse {
  sender: Sender;
  mute: boolean;
  roomId: number;
}
export interface IChatSearchPayload {
  page: number;
  size: number;
  search: string;
  sortingColumns: SortingColumn[];
  keyword: string;
  roomId?: number;
}

export interface SortingColumn {
  sortOrder: number;
  columnName: string;
}

export interface IChatSearchResponse {
  page: number;
  count: number;
  totalCount: number;
  data: ChatSearchData[];
}

export interface IChatSearchList {
  chatUser: any;
  unreadMessageCount: number;
  lastMessage: any;
  lastMessageSentTime: any;
  entireLastMessage: any;
  roomId: number;
  roomName: string;
  roomMembersCount: number;
  roomProfileUrl: any;
  roomCreatorId: string;
  lastMessageObj: any;
  leavedUserId: any;
  joinedUserId: any;
  chatType: string;
  roomChatType: RoomChatType;
  departmentName: any;
  equipmentId: any;
  ticketId: number;
  propertyName: any;
  currentUserType: string;
  roomCreatedTime: number;
  isArchived: boolean;
  isUserLeft: boolean;
  userLeft: boolean;
  mute: boolean;
  archived: boolean;
}

//* ------ END SOCKET EVENT ------
