import React from "react";
import { Form } from "react-bootstrap";

interface RxSwitchProps {
  checked?: boolean;
  onChange?: (checked: boolean) => void;
  disabled?: boolean;
  className?: string;
  id?: string;
  label?: string;
  size?: "sm" | "lg";
}

const RxSwitch: React.FC<RxSwitchProps> = ({
  checked = false,
  onChange,
  disabled = false,
  className = "",
  id,
  label,
  size,
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (onChange) {
      onChange(e.target.checked);
    }
  };

  const sizeClass = size ? `form-switch-${size}` : "";

  return (
    <Form.Check
      type="switch"
      id={id}
      label={label}
      checked={checked}
      onChange={handleChange}
      disabled={disabled}
      className={`rx-switch ${sizeClass} ${className}`}
    />
  );
};

export default RxSwitch;
