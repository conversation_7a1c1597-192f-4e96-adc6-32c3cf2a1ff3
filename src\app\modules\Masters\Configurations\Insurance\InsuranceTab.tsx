import { orderBy } from "@progress/kendo-data-query";
import { GridColumn as Column, Grid } from "@progress/kendo-react-grid";
import { Tooltip } from '@progress/kendo-react-tooltip';
import { useState } from 'react';
import { Dropdown } from "react-bootstrap";
import { FaFileDownload, FaQrcode, FaRegEye, } from 'react-icons/fa';
import { IoMdAdd, IoMdMore } from 'react-icons/io';
import { IoFilter } from "react-icons/io5";
import { MdOutlineDeleteForever, MdOutlineEdit } from "react-icons/md";
import { Link } from 'react-router-dom';


function InsuranceTab() {
    const initialSort: Array<any> = [
        { field: "User Name", dir: "asc" },
    ];
    const [sort, setSort] = useState(initialSort);


    const company_data = [
        {
            id: 1,
            title: 'Admin Access',
            members: '<PERSON>, <PERSON>',
            active: true,
        },
        {
            id: 2,
            title: 'Editor Access',
            members: '<PERSON>, <PERSON>',
            active: false,
        },
        {
            id: 3,
            title: 'Viewer Access',
            members: '<PERSON>',
            active: true,
        },

    ]
    const [data, setdata] = useState(company_data)
    const renderswitch = (id: any, data: any) => {
        return (
            <td>
                <div className="d-flex align-items-center">
                    <div className="me-3">
                        <input
                            id={id}
                            type="checkbox"
                            className="checkbox"
                        />

                        <label htmlFor={id} className="switch">
                            <span className="switch__circle">
                                <span className="switch__circle-inner"></span>
                            </span>
                            <span className="switch__left">Active</span>
                            <span className="switch__right">Inactive</span>
                        </label>
                    </div>
                </div>
            </td>
        );
    };
    // const dispatch = useDispatch();
    // const gridData = useSelector((state:any) => state.lock.data.gridData)
    // useEffect(() => {
    //   dispatch(getGridDataAsync())
    // }, [dispatch])

    // const [data, setdata] = useState(gridData)
    // Pagination start
    const initialDataState: any = { skip: 0, take: 10 };
    const [page, setPage] = useState<any>(initialDataState);
    const [pageSizeValue, setPageSizeValue] = useState<
        number | string | undefined
    >();
    const pageChange = (event: any) => {
        const targetEvent = event.targetEvent as any;
        const take =
            targetEvent.value === "All" ? data.length : event.page.take;

        if (targetEvent.value) {
            setPageSizeValue(targetEvent.value);
        }
        setPage({
            ...event.page,
            take,
        });
    };

    // Pagination End

    const renderaction = ({ content }: any) => {
        return (
            <td className="k-table-td">

                <Dropdown className="new-chat-btn" style={{ position: 'static' }}>
                    <Dropdown.Toggle
                        as="span"
                        className="fs-1 cursor-pointer ms-2"
                    >
                        <IoMdMore className="td-icon cursor-pointer" />
                    </Dropdown.Toggle>
                    <Dropdown.Menu align="end">
                        <Dropdown.Item >
                            <span className="fs-5">
                                <MdOutlineEdit className="me-4" size={18} />
                                Edit
                            </span>
                        </Dropdown.Item>
                        <Dropdown.Item >
                            <span className="fs-5">
                                <FaRegEye className="me-4" size={16} />
                                View
                            </span>
                        </Dropdown.Item>
                        <Dropdown.Item>
                            <span className="fs-5">
                                <MdOutlineDeleteForever className="me-4" size={18} />
                                Delete
                            </span>
                        </Dropdown.Item>
                    </Dropdown.Menu>
                </Dropdown>
            </td>
        );
    };
    // end action

    // start image
    const renderQrcode = () => {
        return <td className='text-center'><FaQrcode className='td-icon cursor-pointer ' /></td>
    }
    // end image

    //  start tooltip
    const renderTooltipCell = (props: any) => {
        const { dataItem, field, content } = props;
        return (
            <td className='k-table-td'>
                <span className='ellipsis-cell' title={content}>{dataItem[field]}</span>
            </td>
        );
    };

    // end tooltip



    return (
        <>
            <div className='row pageheader mb-7'>
                <div className=' col-xl-6 col-lg-6 col-sm-6   mt-auto mb-auto'>
                    <h1 className='page-title mobile-margin mb-0'>
                        Insurance
                    </h1>
                </div>
                <div className=" col-xl-6 col-lg-8 col-sm-6 mt-auto mb-auto text-end">
                    <input
                        type="search"
                        className="d-none" //form-control search-box mobile-margin
                        placeholder="Search..."
                    />
                    <Link
                        to={""}
                        className="btn rx-btn ms-3 mobile-margin mb-lg-3"

                    >
                        <IoFilter className="btn-icon-custom" />
                        Filter{" "}
                    </Link>
                    <Link to={""} className="btn rx-btn ms-3 mobile-margin mb-lg-3" >
                        <FaFileDownload className="btn-icon-custom" />

                        Export
                    </Link>
                    <Link to={""} className="btn rx-btn ms-3 mobile-margin mb-lg-3" >
                        <IoMdAdd className="btn-icon-custom" />
                        Add{" "}
                    </Link>
                </div>
            </div >
            <div className='card mt-0'>
                <div className='card-body p-0'>
                    <div className='table_div saftyform-table1' style={{ width: '100%' }}>
                        <Tooltip position="bottom" anchorElement="target">
                            <Grid data={orderBy(company_data, sort).slice(page.skip, page.skip + page.take)}
                                skip={page.skip}
                                take={page.take}
                                total={data.length}
                                pageable={{
                                    buttonCount: 4,
                                    pageSizes: [5, 10, 15, "All"],
                                    pageSizeValue: pageSizeValue,
                                    // responsive:true,
                                }}

                                onPageChange={pageChange}
                                sortable={true}
                                sort={sort}
                                onSortChange={(e: any) => {
                                    setSort(e.sort);
                                }}
                            >
                                <Column title='Name' field='title' cell={(props) => renderTooltipCell({ ...props, content: props.dataItem.lockNumber })} />
                                <Column title='Member' field='members' cell={(props) => renderTooltipCell({ ...props, content: props.dataItem.lockNumber })} />
                                <Column title='Active' field='active' cell={(props) =>
                                    renderswitch(
                                        props.dataItem.id,
                                        props.dataItem
                                    )
                                } />
                                <Column title='Action' width={120} cell={(props) => renderaction({ ...props, content: props.dataItem.id })} />
                            </Grid>
                        </Tooltip>
                    </div>
                </div>
            </div>

        </>
    )
}

export default InsuranceTab;