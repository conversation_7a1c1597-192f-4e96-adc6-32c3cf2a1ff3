import React from "react";
import { Formik, Form, Field } from "formik";
import FormSwitch from "../../../component/form/FormSwitch";
import FormButton from "../../../component/form/FormButton";
import Footer from "./Footer";
import { useSetSurveyConfigurationMutation } from "../../../apis/survaysAPI";
import useSurveyUtil from "../helper/useDetectSurvayType";

const contactMethods = ["Text", "Call", "Email"];
const contactTimes = ["Morning", "Afternoon", "Evening"];

// const validationSchema = Yup.object().shape({
//   wantOutsideContact: Yup.boolean().required(),
//   customerName: Yup.boolean().when(
//     "wantOutsideContact",
//     (wantOutsideContact, schema) =>
//       wantOutsideContact ? schema.required() : schema
//   ),
//   customerPhone: Yup.boolean().when(
//     "wantOutsideContact",
//     (wantOutsideContact, schema) =>
//       wantOutsideContact ? schema.required() : schema
//   ),
//   customerEmail: Yup.boolean().when(
//     "wantOutsideContact",
//     (wantOutsideContact, schema) =>
//       wantOutsideContact ? schema.required() : schema
//   ),
//   isContactPref: Yup.boolean().when(
//     "wantOutsideContact",
//     (wantOutsideContact, schema) =>
//       wantOutsideContact ? schema.required() : schema
//   ),
//   contactPrefType: Yup.string().when(
//     ["wantOutsideContact", "isContactPref"],
//     (wantOutsideContact, isContactPref) =>
//       wantOutsideContact && isContactPref
//         ? Yup.string().required("Select a contact preference")
//         : Yup.string()
//   ),
//   isContactTime: Yup.boolean().when(
//     "wantOutsideContact",
//     (wantOutsideContact, schema) =>
//       wantOutsideContact ? schema.required() : schema
//   ),
//   contactTimeType: Yup.string().when(
//     ["wantOutsideContact", "isContactTime"],
//     (wantOutsideContact, isContactTime) =>
//       wantOutsideContact && isContactTime
//         ? Yup.string().required("Select a preferred contact time")
//         : Yup.string()
//   ),
// });

const initialValues = {
  wantOutsideContact: false,
  customerName: false,
  customerPhone: false,
  customerEmail: false,
  isContactPref: false,
  contactPrefType: "Email",
  isContactTime: false,
  contactTimeType: "Afternoon",
};

interface Props {
  handleNext: () => void;
  handleBack: () => void;
}

const SurveyConfiguration: React.FC<Props> = ({ handleNext, handleBack }) => {
  const { surveyId } = useSurveyUtil();
  const [setSurveyConfiguration, { isLoading }] = useSetSurveyConfigurationMutation();
  return (
    <div className="mb-5">
      <Formik
        initialValues={initialValues}
        // validationSchema={validationSchema}
        onSubmit={(values) => {
          // alert(JSON.stringify(values, null, 2));
        }}
      >
        {({ values }) => (
          <Form>
            <div className="custom-card p-5 mb-5 mt-5">
              <div className="d-flex gap-2 align-items-center mb-5">
                <Field type="checkbox" name="wantOutsideContact" />
                <p className="m-0">Do you want outside contact details?</p>
              </div>

              {values.wantOutsideContact && (
                <div style={{ maxWidth: 800 }}>
                  <div className="p-4 response-viewer-box-wrapper mb-4 d-flex align-items-center justify-content-between">
                    <p className="m-0">Customer Name</p>
                    <FormSwitch name="customerName" />
                  </div>
                  <div className="p-4 response-viewer-box-wrapper mb-4 d-flex align-items-center justify-content-between">
                    <p className="m-0">Customer Phone</p>
                    <FormSwitch name="customerPhone" />
                  </div>
                  <div className="p-4 response-viewer-box-wrapper mb-4 d-flex align-items-center justify-content-between">
                    <p className="m-0">Customer Email</p>
                    <FormSwitch name="customerEmail" />
                  </div>

                  <div className="mb-1 d-flex align-items-center justify-content-between pe-4 mb-5">
                    <p className="m-0">Customer Contact Preferences</p>
                    <FormSwitch name="isContactPref" />
                    {values.isContactPref && (
                      <div className="d-flex mt-3">
                        {contactMethods.map((method) => (
                          <label
                            key={method}
                            style={{ marginRight: 16 }}
                            className="d-flex align-items-center gap-2"
                          >
                            <Field
                              type="radio"
                              name="contactPrefType"
                              value={method}
                            />{" "}
                            {method}
                          </label>
                        ))}
                      </div>
                    )}
                  </div>

                  <div className="mb-1 d-flex align-items-center justify-content-between pe-4">
                    <p className="m-0">Customer Preferred Contact Time</p>
                    <FormSwitch name="isContactTime" />
                    {values.isContactTime && (
                      <div className="d-flex mt-3">
                        {contactTimes.map((time) => (
                          <label
                            key={time}
                            style={{ marginRight: 16 }}
                            className="d-flex align-items-center gap-2"
                          >
                            <Field
                              type="radio"
                              name="contactTimeType"
                              value={time}
                            />
                            <span>{time}</span>
                          </label>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>

            {/* <FormButton type="submit" className="btn btn-primary mt-3">
                Submit
              </FormButton> */}
            <Footer
              handleBack={handleBack}
              handleNext={handleNext}
              saveLabel="Create Survey"
            />
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default SurveyConfiguration;
