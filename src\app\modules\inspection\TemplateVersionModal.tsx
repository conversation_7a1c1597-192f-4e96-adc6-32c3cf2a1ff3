import { Mo<PERSON>, <PERSON>, <PERSON> } from "react-bootstrap";
import { SlClose } from "react-icons/sl";
import InspectionTemplate from "../pages/InspectionTemplate";

interface Props {
  show: boolean;
  onHide: () => void;
  templateId: string;
  title: string;
}

const TemplateVersionModal: React.FC<Props> = ({ show, onHide, templateId, title }) => {
  return (
    <Modal 
      show={show} 
      onHide={onHide} 
      dialogClassName="modal-90w"
      className="modal-right modal-right-full"
      scrollable={true}

    >
      <Modal.Header className="p-0">
        <Row className="w-100">
          <Col xs={6} className="mt-auto mb-auto">
            <h2 className="mb-0">{title}</h2>
          </Col>
          <Col xs={6} className="text-end mb-3">
            <span className="btn btn-dark" onClick={onHide}>
              <SlClose className="btn-icon-custom" />
              Close
            </span>
          </Col>
        </Row>
      </Modal.Header>
      <Modal.Body className="">
        <InspectionTemplate versionId={templateId} viewOnly={true} />
      </Modal.Body>
    </Modal>
  );
};

export default TemplateVersionModal;

