import { useEffect, useState } from "react";
import OTPInput from "react-otp-input";
import { Link, useNavigate } from "react-router-dom";
import logo from "../../../efive_assets/images/Logo.png";
import "././twofactorauth/OtpInput.css";
import LoginSidePanal from "./LoginSidePanal";
// import CountdownTimer from "../common/CountdownTimer";
import OtpTimer from "otp-timer";
import { FaRegQuestionCircle } from "react-icons/fa";
import { useSelector } from "react-redux";
import { ClipLoader } from "react-spinners";
import FadeLoader from "react-spinners/FadeLoader";
import { ToastContainer } from "react-toastify";
import { otpVarify, resendOTP } from "../../redux/authSlice";
import { useAppDispatch } from "../../redux/store";
import SwalMessage from "../common/SwalMessage";
const BACKEND_BASE_URL = import.meta.env.VITE_REACT_BACKEND_URL;


// import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
// import { faCheckCircle } from "@fortawesome/free-solid-svg-icons";

const Login = () => {
  const navigate = useNavigate();
  const [otp, setOtp] = useState("");
  const [otpError, setOtpError] = useState("");
  const [serverMsg, setServerMsg] = useState("");
  const [isOtpResend, setIsOtpResend] = useState(false);
  const [isLoginFailed, setIsLoginFailed] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const dispatch = useAppDispatch();
  const { mobilenumber } = useSelector(
    (state: any) => state?.auth?.basicUserInfo
  );
  const spinner = (
    <div className="spinner-page">
      <ClipLoader size={60} className="spinner" />
    </div>
  );
  const isDisabled = otp.length !== 6;
  const onChange = (value: string) => {
    setOtp(value);
    if (otpError) setOtpError("");
  };

  const handleOtpResend = async () => {
    setOtp("");
    try {
      let result: response = await dispatch(resendOTP()).unwrap();
      if (result.success == true) {
        setIsOtpResend(true);
        setIsLoading(false);
        setServerMsg(result.errormsg);
        setIsLoginFailed(false);
      } else {
        SwalMessage(null, result?.errormsg, "Ok", "error", false);
        setIsLoading(false);
        setIsOtpResend(false);
        setIsLoginFailed(true);
      }
    } catch (error) {
      console.devAPILog("error", error);
    }
  };

  type response = {
    success: boolean;
    errormsg: string;
    data: any;
  };
  const otpVerifyPayload = {
    otpemail: otp,
    otpmobile: null,
  };
  const handleOTPVerification = async () => {
    setIsLoading(true);
    try {
      let result: response = await dispatch(
        otpVarify(otpVerifyPayload)
      ).unwrap();

      if (result.success == true) {
        localStorage.setItem("islogin", "true");
        sessionStorage.setItem("islogin", "true");
        const userType: any = localStorage.getItem("userinfo");
        const newUserType = JSON.parse(userType);
        if (newUserType?.profileimage) {
          const imageUrl =
            BACKEND_BASE_URL +
            "resources/files" +
            newUserType?.profileimage;
          sessionStorage.setItem("imageUrl", imageUrl);
        }
        sessionStorage.setItem("fullName", newUserType.displayusername);
        sessionStorage.setItem("emailId", newUserType.displayemail);
        sessionStorage.setItem("userId", newUserType.userid);
        if (newUserType?.displayusertype == "SUPER_ADMIN") {
          setIsLoading(false)
          navigate("/admindashboard");
          // navigate(0);
        } else {
          navigate("/dashboard");
          // navigate(0);
        }
      } else {
        setIsLoginFailed(true);
        setIsLoading((false))
        SwalMessage(null, result?.errormsg, "Ok", "error", false);
        setServerMsg(
          result.errormsg || "Invalid verification code. Please try again."
        );
      }
    } catch (error) {
      console.error(error);
      setIsLoading(false)
      setIsLoginFailed(true);
      setServerMsg("Something Went Wrong. Please Try Again.");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (otp.length === 6) {
      handleOTPVerification();
    }
  }, [otp]);

  return (
    <>
      {isLoading && spinner}

      <div className="d-flex flex-column flex-lg-row flex-column-fluid login-bg login-page-height">
        <LoginSidePanal pagelabel="" />
        <div className="d-flex flex-column flex-lg-row-fluid p-10 login-right ">
          <div className="d-flex flex-center flex-column flex-lg-row-fluid">
            <Link to={""} className="need-help-text">
              <FaRegQuestionCircle className="need-help-icon" />
              Need Help?
            </Link>
            {/* {isVisible1 && ( */}
            <div className="form-width-factor p-10 mobile-padding">
              <form className="form w-100">
                <div className="text-center mobile-logo-div mb-3">
                  <img src={logo} className="logo-image-mobile" />
                </div>
                <div className="mb-15 mobile-margin-bottom-0 mobile-text-center">
                  <span className="right-main-header">
                    Two - Factor Authentication
                  </span>
                </div>

                <div className="text-center ">
                  <span className="otp-heade-one"> We've sent you a code</span>
                </div>
                <div className="text-center mb-15 mobile-margin-bottom-0">
                  <span className="otp-heade-two">
                    Please check your phone number{" "}
                    <span className="contact-text"> {mobilenumber}</span>
                  </span>
                </div>

                <div className="fv-row otp-field mb-8 d-flex justify-content-center">
                  <OTPInput
                    inputStyle={{
                      border: "1px solid white",
                      borderRadius: "12px",
                      width: "42px",
                      height: "42px",
                      fontSize: "22px",
                      color: "#ffffff ",
                      fontWeight: "600",
                      backgroundColor: "transparent",
                      fontFamily: "Inter",
                      marginTop: "10px",
                    }}
                    value={otp}
                    onChange={onChange}
                    numInputs={6}
                    renderSeparator={<span className="otp-box-margin"></span>}
                    shouldAutoFocus={true}
                    renderInput={(props) => (
                      <input
                        {...props}
                        name=""
                        inputMode="numeric"
                        pattern="[0-9]*"
                        autoComplete="one-time-code"
                        onKeyPress={(e) => {
                          if (!/[0-9]/.test(e.key)) {
                            e.preventDefault();
                          }
                        }}
                      />
                    )}
                  />
                </div>

                <div className="text-center otpdiv text-white mb-6">
                  <OtpTimer
                    text={"Resend Code in : "}
                    ButtonText={"Resend Code"}
                    seconds={59}
                    minutes={1}
                    resend={handleOtpResend}
                    buttonColor={"#ffffff"}
                    background={"rgb(255 255 255 / 0%)"}
                  />
                </div>
                <div className="fv-row mb-5 d-flex justify-content-center spinner">
                  <FadeLoader
                    margin={-5}
                    height={10}
                    width={3}
                    color="#fff"
                    loading={isLoading}
                    className="ms-7"
                  />
                </div>

                <div>
                  {otpError && (
                    <div className="text-center text-danger mb-3">
                      <span style={{ whiteSpace: "pre-line" }}>{otpError}</span>
                    </div>
                  )}
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>

    </>
  );
};

export default Login;
