import React, { useEffect, useState } from "react";
import { LinkModal } from "../../../shared/components/LinkModal/index";
import { LinkItem, WebLink } from "../../../shared/components/LinkModal/types";
import SwalMessage from "../../common/SwalMessage";
import { BsTicket } from "react-icons/bs";
import { useNavigate } from "react-router-dom";
// No need for DataStatusWrapper
import {
  useGetLinkedTicketsMutation,
  useLinkTicketsMutation,
  useLinkWebLinksMutation,
  useDeleteLinkWeblinkMutation,
} from "../../../apis/inspectionLinkItemsAPI";
import { useGetTicketListMutation } from "../../../apis/ticketListAPI";
import { Loader } from "../../../component";
// Helper functions for status and priority colors
const getStatusColor = (status: string): string => {
  switch (status?.toLowerCase()) {
    case "open":
      return "danger";
    case "in progress":
      return "warning";
    case "closed":
      return "success";
    default:
      return "primary";
  }
};

const getPriorityColor = (priority: string): string => {
  switch (priority?.toLowerCase()) {
    case "high":
      return "danger";
    case "medium":
      return "warning";
    case "low":
      return "success";
    default:
      return "primary";
  }
};

interface InspectionLinkModalProps {
  open: boolean;
  onClose: () => void;
  inspectionId: string;
}

const InspectionLinkModal: React.FC<InspectionLinkModalProps> = ({
  open,
  onClose,
  inspectionId,
}) => {
  const navigate = useNavigate();
  const [items, setItems] = useState<LinkItem[]>([]);
  const [webLinks, setWebLinks] = useState<WebLink[]>([]);

  // API mutations
  const [getLinkedTickets, { isLoading: isLoadingLinkedTickets }] =
    useGetLinkedTicketsMutation();
  const [linkTickets, { isLoading: isLoadingLinkTickets }] =
    useLinkTicketsMutation();
  const [linkWebLinks, { isLoading: isLoadingLinkWebLinks }] =
    useLinkWebLinksMutation();
  const [deleteLinkWeblink, { isLoading: isLoadingDeleteWeblink }] =
    useDeleteLinkWeblinkMutation();
  const [getTicketList, { isLoading: isLoadingTicketList }] =
    useGetTicketListMutation();

  // Fetch linked items when modal opens
  useEffect(() => {
    if (open && inspectionId) {
      fetchLinkedItems();
    }
  }, [open, inspectionId]);

  // Fetch linked items from API
  const fetchLinkedItems = async (searchTerm: string = "") => {
    try {
      const response = await getLinkedTickets({
        inspectionId,
        search: searchTerm,
      }).unwrap();

      if (response.success) {
        // Map API response to LinkItem format
        const ticketItems: LinkItem[] = response.data.linkticketgriddata.map(
          (ticket) => ({
            id: ticket.ticketId,
            title: ticket.ticketNumber,
            subtitle: ticket.ticketSummary,
            icon: <BsTicket />,
            status: ticket.currentStatus,
            statusColor: getStatusColor(ticket.currentStatus),
            priority: ticket.priority,
            priorityColor: getPriorityColor(ticket.priority),
            createdOn: ticket.createdon,
            assignedUsers: ticket.assignedusers?.map((user) => ({
              id: user.value,
              name: user.label,
              image: user.image,
            })),
          })
        );

        // Map API response to WebLink format
        const webLinkItems: WebLink[] = response.data.weblinkgriddata.map(
          (link) => ({
            id: link.weblinkid,
            title: link.webtitle,
            url: link.weburl,
          })
        );

        setItems(ticketItems);
        setWebLinks(webLinkItems);
      } else {
        SwalMessage(
          null,
          response.errormsg || "Failed to fetch linked items",
          "Ok",
          "error",
          false
        );
      }
    } catch (error) {
      console.error("Error fetching linked items:", error);
      SwalMessage(null, "Failed to fetch linked items", "Ok", "error", false);
    }
  };

  // Handle search
  const handleSearch = async (searchTerm: string) => {
    if (open && inspectionId) {
      fetchLinkedItems(searchTerm);
    }
  };

  // Get available tickets for selection
  const getAvailableItems = async (searchTerm: string): Promise<LinkItem[]> => {
    try {
      const response = await getTicketList({
        page: 0,
        size: 10,
        search: searchTerm,
        inspectionId,
        sortingColumns: [
          {
            sortOrder: 0,
            columnName: "createdon",
          },
        ],
      }).unwrap();

      if (response.success) {
        // Map ticket data to LinkItem format
        const availableItems: LinkItem[] = response.data.data.map((ticket) => ({
          id: ticket.ticketid,
          title: ticket.ticketnumber,
          subtitle: ticket.summary,
          icon: <BsTicket />,
          status: ticket.currentstatus,
          statusColor: getStatusColor(ticket.currentstatus),
          priority: ticket.priority,
          priorityColor: getPriorityColor(ticket.priority),
          createdOn: ticket.createdon,
          assignedUsers: ticket.assignToMember
            ? [
                {
                  id: ticket.assignToMember.id,
                  name: `${ticket.assignToMember.firstName} ${ticket.assignToMember.lastName}`,
                  image: ticket.assignToMember.imageUrl,
                },
              ]
            : [],
        }));

        return availableItems;
      }

      return [];
    } catch (error) {
      console.error("Error fetching available items:", error);
      return [];
    }
  };

  // Add tickets
  const handleAddItem = async (ticketIds: string[]) => {
    try {
      const response = await linkTickets({
        inspectionId,
        linkedTicketids: ticketIds,
      }).unwrap();

      if (response.success) {
        SwalMessage(
          null,
          "Tickets linked successfully",
          "Ok",
          "success",
          false
        );
        fetchLinkedItems(); // Refresh the list
      } else {
        SwalMessage(
          null,
          response.errormsg || "Failed to link tickets",
          "Ok",
          "error",
          false
        );
      }
    } catch (error) {
      console.error("Error linking tickets:", error);
      SwalMessage(null, "Failed to link tickets", "Ok", "error", false);
    }
  };

  // Add web link
  const handleAddWebLink = async (webLink: { title: string; url: string }) => {
    try {
      const response = await linkWebLinks({
        inspectionId,
        title: webLink.title,
        url: webLink.url,
      }).unwrap();

      if (response.success) {
        SwalMessage(
          null,
          "Web link added successfully",
          "Ok",
          "success",
          false
        );
        fetchLinkedItems(); // Refresh the list
      } else {
        SwalMessage(
          null,
          response.errormsg || "Failed to add web link",
          "Ok",
          "error",
          false
        );
      }
    } catch (error) {
      console.error("Error adding web link:", error);
      SwalMessage(null, "Failed to add web link", "Ok", "error", false);
    }
  };

  // Delete web link
  const handleDeleteWebLink = async (webLinkId: string) => {
    try {
      const response = await deleteLinkWeblink({
        weblinkid: webLinkId,
      }).unwrap();

      if (response.success) {
        SwalMessage(
          null,
          "Web link deleted successfully",
          "Ok",
          "success",
          false
        );
        fetchLinkedItems(); // Refresh the list
      } else {
        SwalMessage(
          null,
          response.errormsg || "Failed to delete web link",
          "Ok",
          "error",
          false
        );
      }
    } catch (error) {
      console.error("Error deleting web link:", error);
      SwalMessage(null, "Failed to delete web link", "Ok", "error", false);
    }
  };

  // Handle item click (navigate to ticket details)
  const handleItemClick = (itemId: string) => {
    window.open(`/tickets/details/${itemId}`, "_blank");
  };

  // Combine all loading states from RTK Query hooks
  const isLoadingAny =
    isLoadingLinkedTickets ||
    isLoadingLinkTickets ||
    isLoadingLinkWebLinks ||
    isLoadingDeleteWeblink ||
    isLoadingTicketList;

  return (
    <div>
      <LinkModal
        isOpen={open}
        onClose={() => onClose()}
        onSearch={handleSearch}
        onAddItem={handleAddItem}
        onAddWebLink={handleAddWebLink}
        onDeleteWebLink={handleDeleteWebLink}
        onItemClick={handleItemClick}
        items={items}
        webLinks={webLinks}
        isLoading={isLoadingAny}
        setIsLoading={() => {}} // Empty function since we're using RTK Query loading states
        title="Link Items"
        canAddItems={true}
        canDeleteItems={true}
        itemsTitle="Tickets"
        webLinksTitle="Web Links"
        searchPlaceholder="Search tickets..."
        getAvailableItems={getAvailableItems}
      />
    </div>
  );
};

export default InspectionLinkModal;
