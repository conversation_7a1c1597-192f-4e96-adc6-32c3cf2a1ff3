import React, { useState, useRef, useEffect } from "react";
import { Field, FieldArray, FormikProps } from "formik";
import { DragDropContext, Draggable, Droppable } from "react-beautiful-dnd";
import { Block } from "./inspection";
import { FiPlus } from "react-icons/fi";
import FormErrorMessage from "../../component/form/FormErrorMessage";
import QuestionAttchmentInput from "./QuestionAttchmentInput";
import FormLabel from "../../component/form/FormLabel";
import { MdOutlineDeleteForever } from "react-icons/md";
import { RxDragHandleHorizontal } from "react-icons/rx";
import { KeyEvents, getModifierKeys } from "../../utils/keyEvents";
import { autoCapitalizeWithSpecialChar } from "../../utils/CommonUtils";

interface QuestionSecionProps {
  blockIndex: number;
  block: Block;
  formik: FormikProps<any>;
  viewOnly?: boolean;
}

const QuestionSection: React.FC<QuestionSecionProps> = ({
  blockIndex,
  block,
  formik,
  viewOnly = false,
}) => {
  const { setFieldValue } = formik;
  const [focusQuestionIndex, setFocusQuestionIndex] = useState<number | null>(
    null
  );
  const questionRefs = useRef<{ [key: number]: HTMLInputElement | null }>({});

  const handleQuestionReorder = (result: any) => {
    if (!result.destination) return;

    const reorderedQuestions = [...block.inspectionTemplateQuestionRequestDtos];
    const [removed] = reorderedQuestions.splice(result.source.index, 1);
    reorderedQuestions.splice(result.destination.index, 0, removed);

    reorderedQuestions.forEach(
      (question, index) => (question.questionOrder = index + 1)
    );

    setFieldValue(
      `inspectionTemplateAreaRequestDtos[${blockIndex}].inspectionTemplateQuestionRequestDtos`,
      reorderedQuestions
    );
  };

  // Function to add a new question and focus on it
  const addNewQuestion = (addQuestion: Function) => {
    const currentQuestions = block.inspectionTemplateQuestionRequestDtos;
    const maxOrder = currentQuestions.length
      ? Math.max(...currentQuestions.map((q) => q.questionOrder))
      : 0;
    addQuestion({
      questionOrder: maxOrder + 1,
      questionText: "",
      inspectionTemplateQuestionAttachmentDto: [],
    });
    // Set focus to the new question after it's added
    setFocusQuestionIndex(currentQuestions.length);
  };

  // Function to remove a question and focus on the previous one
  const removeQuestionAndFocusPrevious = (
    removeQuestion: Function,
    questionIndex: number
  ) => {
    if (block.inspectionTemplateQuestionRequestDtos.length > 1) {
      removeQuestion(questionIndex);
      // Focus on the previous question (or first question if removing the first one)
      const previousIndex = questionIndex > 0 ? questionIndex - 1 : 0;
      setFocusQuestionIndex(previousIndex);
    }
  };

  // Handle key press events using switch case
  const handleKeyDown = (
    e: React.KeyboardEvent,
    addQuestion: Function,
    removeQuestion: Function,
    questionIndex: number,
    currentValue: string
  ) => {
    const modifiers = getModifierKeys(e);
    const hasMultipleQuestions =
      block.inspectionTemplateQuestionRequestDtos.length > 1;
    const isEmptyField = currentValue === "";

    switch (e.key) {
      case KeyEvents.ENTER:
        if (!modifiers.shift) {
          e.preventDefault();
          addNewQuestion(addQuestion);
        }
        break;

      case KeyEvents.BACKSPACE:
        if (isEmptyField && hasMultipleQuestions) {
          e.preventDefault();
          removeQuestionAndFocusPrevious(removeQuestion, questionIndex);
        }
        break;

      case KeyEvents.DELETE:
        if (isEmptyField && hasMultipleQuestions) {
          e.preventDefault();
          removeQuestion(questionIndex);
          // For Delete key, focus on the same index (which will be the next question after deletion)
          // or the previous one if we're deleting the last question
          const nextFocusIndex =
            questionIndex <
            block.inspectionTemplateQuestionRequestDtos.length - 1
              ? questionIndex
              : questionIndex - 1;
          setFocusQuestionIndex(nextFocusIndex >= 0 ? nextFocusIndex : 0);
        }
        break;

      default:
        // No action needed for other keys
        break;
    }
  };

  // Effect to focus on the specified question input
  useEffect(() => {
    if (focusQuestionIndex !== null) {
      // Use setTimeout to ensure the DOM has been updated after question addition/removal
      setTimeout(() => {
        if (questionRefs.current[focusQuestionIndex]) {
          questionRefs.current[focusQuestionIndex]?.focus();
        }
        setFocusQuestionIndex(null);
      }, 0);
    }
  }, [focusQuestionIndex, block.inspectionTemplateQuestionRequestDtos.length]);

  return (
    <FieldArray
      name={`inspectionTemplateAreaRequestDtos[${blockIndex}].inspectionTemplateQuestionRequestDtos`}
    >
      {({ push: addQuestion, remove: removeQuestion }) => (
        <>
          {viewOnly ? (
            <div className="mx-5 pb-5 mt-5">
              {block.inspectionTemplateQuestionRequestDtos
                .sort((a, b) => a.questionOrder - b.questionOrder)
                .map((question, questionIndex) => (
                  <div
                    key={question.questionOrder}
                    className="question-container"
                  >
                    <FormLabel>Question {questionIndex + 1}</FormLabel>
                    <div className="question-no-wrapper">
                      <Field
                        name={`inspectionTemplateAreaRequestDtos[${blockIndex}].inspectionTemplateQuestionRequestDtos[${questionIndex}].questionText`}
                        placeholder="Enter Question Text"
                        className="form-control"
                        disabled={viewOnly}
                        readOnly={viewOnly}
                      />
                    </div>
                    <FormErrorMessage
                      name={`inspectionTemplateAreaRequestDtos[${blockIndex}].inspectionTemplateQuestionRequestDtos[${questionIndex}].questionText`}
                    />
                    <div className="question-attachment-input-wrapper">
                      <QuestionAttchmentInput
                        blockIndex={blockIndex}
                        questionIndex={questionIndex}
                        question={question}
                        formik={formik}
                        viewOnly={viewOnly}
                      />
                    </div>
                  </div>
                ))}
            </div>
          ) : (
            <DragDropContext onDragEnd={handleQuestionReorder}>
              <Droppable
                droppableId={`inspectionTemplateQuestionRequestDtos-${blockIndex}`}
              >
                {(droppableProvided, droppableSnapshot) => (
                  <div
                    {...droppableProvided.droppableProps}
                    ref={droppableProvided.innerRef}
                    className="mx-5 mb-5 mt-2"
                    style={{
                      background: droppableSnapshot.isDraggingOver
                        ? "rgba(211, 211, 211, 0.3)"
                        : "transparent",
                      borderRadius: droppableSnapshot.isDraggingOver
                        ? "8px"
                        : 0,
                      padding: droppableSnapshot.isDraggingOver ? "1.25rem" : 0,
                    }}
                  >
                    {block.inspectionTemplateQuestionRequestDtos
                      .sort((a, b) => a.questionOrder - b.questionOrder)
                      .map((question, questionIndex) => (
                        <Draggable
                          key={question.questionOrder}
                          draggableId={`question-${blockIndex}-${question.questionOrder}`}
                          index={questionIndex}
                        >
                          {(provided, snapshot) => (
                            <div
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              className="question-container"
                              style={{
                                userSelect: "none",
                                background: snapshot.isDragging
                                  ? droppableSnapshot.isDraggingOver
                                    ? "rgba(0, 255, 0, 0.2)"
                                    : "rgba(255, 0, 0, 0.2)"
                                  : "transparent",
                                border: snapshot.isDragging
                                  ? droppableSnapshot.isDraggingOver
                                    ? "2px dashed #00ff00"
                                    : "2px dashed #ff0000"
                                  : "none",

                                opacity: snapshot.isDragging ? 0.5 : 1,
                                padding: snapshot.isDragging ? "10px" : 0,
                                borderRadius: snapshot.isDragging ? "10px" : 0,
                                ...provided.draggableProps.style,
                              }}
                            >
                              <FormLabel>
                                Question {questionIndex + 1}
                              </FormLabel>
                              <div className="question-no-wrapper">
                                <Field
                                  name={`inspectionTemplateAreaRequestDtos[${blockIndex}].inspectionTemplateQuestionRequestDtos[${questionIndex}].questionText`}
                                  placeholder="Enter Question Text"
                                  className="form-control"
                                  innerRef={(ref: HTMLInputElement) => {
                                    questionRefs.current[questionIndex] = ref;
                                  }}
                                  onKeyDown={(
                                    e: React.KeyboardEvent<HTMLInputElement>
                                  ) => {
                                    const currentValue = e.currentTarget.value;
                                    handleKeyDown(
                                      e,
                                      addQuestion,
                                      removeQuestion,
                                      questionIndex,
                                      currentValue
                                    );
                                  }}
                                >
                                  {({ field, form }: any) => (
                                    <input
                                      {...field}
                                      placeholder="Enter Question Text"
                                      className="form-control"
                                      disabled={viewOnly}
                                      ref={(ref) => {
                                        questionRefs.current[questionIndex] =
                                          ref;
                                      }}
                                      onChange={(e) => {
                                        const value = autoCapitalizeWithSpecialChar(
                                          e.target.value
                                        );
                                        form.setFieldValue(field.name, value);
                                      }}
                                      onKeyDown={(e) => {
                                        const currentValue =
                                          e.currentTarget.value;
                                        handleKeyDown(
                                          e,
                                          addQuestion,
                                          removeQuestion,
                                          questionIndex,
                                          currentValue
                                        );
                                      }}
                                    />
                                  )}
                                </Field>
                                {block.inspectionTemplateQuestionRequestDtos
                                  .length > 1 && (
                                  <div className="d-flex align-items-center gap-2 ps-2">
                                    <button
                                      type="button"
                                      className="btn btn-sm btn-icon delete-icon"
                                      onClick={() =>
                                        removeQuestion(questionIndex)
                                      }
                                    >
                                      <MdOutlineDeleteForever className="fs-2" />
                                    </button>
                                    <span {...provided.dragHandleProps}>
                                      <RxDragHandleHorizontal className="drag-icon" />
                                    </span>
                                  </div>
                                )}
                              </div>
                              <FormErrorMessage
                                name={`inspectionTemplateAreaRequestDtos[${blockIndex}].inspectionTemplateQuestionRequestDtos[${questionIndex}].questionText`}
                              />
                              <div className="question-attachment-input-wrapper">
                                <QuestionAttchmentInput
                                  blockIndex={blockIndex}
                                  questionIndex={questionIndex}
                                  question={question}
                                  formik={formik}
                                />
                              </div>
                            </div>
                          )}
                        </Draggable>
                      ))}
                    {droppableProvided.placeholder}
                  </div>
                )}
              </Droppable>
            </DragDropContext>
          )}

          {!viewOnly && (
            <div className="p-5 pt-0">
              <button
                type="button"
                className="btn mt-3 btn-blue-black rx-btn d-flex align-items-center justify-content-center gap-2"
                onClick={() => addNewQuestion(addQuestion)}
              >
                <FiPlus /> Add Question
              </button>
            </div>
          )}
        </>
      )}
    </FieldArray>
  );
};

export default QuestionSection;
