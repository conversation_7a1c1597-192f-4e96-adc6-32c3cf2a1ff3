import { ChangeEvent, useEffect, useState } from "react";
import { Card, Col, Form, Modal, Row } from "react-bootstrap";
import { BsSave } from "react-icons/bs";
import { IoMdAdd } from "react-icons/io";
import { IoClose, IoMailOutline } from "react-icons/io5";
import { SlClose } from "react-icons/sl";
import { useSelector } from "react-redux";
import { ClipLoader } from "react-spinners";
import usersvg from "../../../_metronic/assets/SideMenuIcon/MenuallSVGIcon/user.svg";
import {
  ThemeModeSwitcher,
  ThemeModeType,
  useThemeMode,
} from "../../../_metronic/partials";
import { useAppDispatch } from "../../redux/store";
import { getUserDetail } from "../../redux/userSlice";
import { APIs } from "../../serverconfig/apiURLs";
import axiosInstance from "../../serverconfig/axiosInstance";
import {
  extensionPacks,
  formatContactInputNumber,
  getImage
} from "../../utils/CommonUtils";
import encryptDecryptUtil from "../../utils/encrypt-decrypt-util";
import SingleSelectDropdown from "./SingleSelectDropdown";
import SwalMessage from "./SwalMessage";

function profile({ profilemodal, setmodal }: any) {
  const spinner = (
    <div className="spinner-page">
      <ClipLoader size={60} className="spinner" />
    </div>
  );
  const dispatch = useAppDispatch();
  const [twoFactorData, settwoFactorData] = useState<any>();
  const [userType, setuserType] = useState<any>();
  const [base64String, setBase64String] = useState<string>("");
  const [imageUrl, setImageUrl] = useState<string>("");
  const [isLoading, setisLoading] = useState(false);
  const [imageChange, setimageChange] = useState(false);
  const [
    twoFAdata, settwoFAdata] = useState<any>();
  const [userdetail, setUserDetail] = useState<any>({});

  const userinfo = localStorage.userinfo
    ? JSON.parse(localStorage.userinfo)
    : {};

  useEffect(() => {
    const usertype: any = localStorage.getItem("userinfo");
    const newUserType = JSON.parse(usertype);
    setuserType(newUserType);
    setimageChange(false);
  }, [profilemodal]);

  // console.log("userdetail.userProfile",userdetail.userProfile);
  useEffect(() => {
    if (imageChange === false) {
      setImageUrl(
        userdetail.userProfile != "" ? getImage(userdetail.userProfile) : ""
      );
    }
  }, [userdetail, imageChange]);
  const {
    displayusername,
    displayemail,
    displaymobilenumber,
    profileimage,
    userid,
  } = useSelector((state: any) => state?.auth.basicUserInfo);

  const fetchTwoFactorData = async () => {
    let keyinfo = JSON.parse(localStorage.keyinfo);
    const response = await axiosInstance.post(APIs.ALL_HEADERS.twoFAdata, null);
    const resData = response.data;
    if (resData) {
      const decryptedData = encryptDecryptUtil.decryptData(resData.data, keyinfo.syckey)
      const parseData = JSON.parse(decryptedData);
      const filteredData =
        userinfo?.companyTwoFaceAuth === 1
          ? parseData.filter((item: any) => item.label !== "None")
          : parseData;
      settwoFAdata(
        filteredData
      );
    }
  };
  useEffect(() => {
    if (twoFAdata?.length > 0 && profilemodal) {
      settwoFactorData(twoFAdata?.find((response: any) => response.value == userdetail?.twoFactorRequired))
    }
  }, [twoFAdata, profilemodal, userdetail])
  useEffect(() => {
    if (profilemodal) {
      fetchTwoFactorData();
      setUserDetail(localStorage.getItem("userdetail") ? JSON.parse(localStorage.getItem("userdetail") || '{}') : {})
    }
    if (!profilemodal) {
      settwoFactorData(null)
    }
  }, [profilemodal]);

  const { menuMode, mode, updateMode, updateMenuMode } = useThemeMode();
  const [IsTheme, setIsTheme] = useState<ThemeModeType>(menuMode);

  const handleSubmit = async () => {
    setisLoading(true);

    if (IsTheme) {
      updateMenuMode(IsTheme);
      updateMode(IsTheme);
    }

    const payload = {
      editid: userid,
      userProfile: base64String ? base64String : imageUrl,
      twoFactorRequired: twoFactorData ? twoFactorData.value : null,
      email: displayemail ? displayemail : "",
      phoneNumber: userdetail?.phone_number ? userdetail?.phone_number : "",
    };

    let keyinfo = JSON.parse(localStorage.keyinfo);
    // console.log("payload", payload);

    let ciphertext = encryptDecryptUtil.encryptData(
      JSON.stringify(payload),
      keyinfo.syckey
    );
    const response = await axiosInstance.post(APIs.ALL_HEADERS.createUser, {
      encryptedData: `${ciphertext}`,
    });
    const resData = response.data;
    if (resData.success == true) {
      setisLoading(false);
      SwalMessage(null, resData.errormsg, "Ok", "success", false);
      await dispatch(getUserDetail({ userid: userinfo.userid })).unwrap();
      // console.log(userdetail);
      setmodal(false);
    } else {
      setisLoading(false);
      SwalMessage(null, resData.errormsg, "Ok", "error", false);
    }
  };
  const handleFileInputChange1 = (event: ChangeEvent<HTMLInputElement>) => {
    const file: any = event.target.files?.[0];
    const fileSize: any = localStorage.getItem("userinfo");
    const newFileSize = JSON.parse(fileSize);
    const isValidFileType = extensionPacks.imageExtensions.includes(file.type);
    const isvalidateFileSize =
      file.size <= newFileSize.uploadimagesize * 1024 * 1024;
    if (file && isvalidateFileSize && isValidFileType) {
      const reader = new FileReader();
      reader.onloadend = () => {
        const result = reader.result;
        if (typeof result === "string") {
          setBase64String(result);
          setImageUrl(result);
          setimageChange(true);
          // console.log(result);
        }
      };
      reader.readAsDataURL(file);
    } else {
      if (!isvalidateFileSize) {
        SwalMessage(
          null,
          `Please upload jpeg | jpg | png images under ${newFileSize.uploadimagesize}MB size`,
          "Ok",
          "error",
          false
        );
      } else {
        SwalMessage(
          null,
          "Please upload only jpeg | jpg | png file.",
          "Ok",
          "error",
          false
        );
      }
    }
  };

  // ** delete image
  const deleteimage = () => {
    SwalMessage(
      null,
      "Are you sure want to delete image?",
      "Yes",
      "warning",
      true
    ).then((result) => {
      if (result) {
        setImageUrl("");
        setimageChange(false);
        setBase64String("");
      }
    });
  };

  return (
    <>
      {isLoading && spinner}
      <Modal
        className="modal-right modal-right-small"
        scrollable={true}
        show={profilemodal}
      >
        <Modal.Header className="p-0">
          <Row className="">
            <Col xs={4} className="mt-auto mb-auto">
              <h2 className="mb-0"> My Profile</h2>
            </Col>
            <Col xs={8} className="text-end mb-3">
              <span
                className="btn rx-btn cursor-pointer"
                onClick={handleSubmit}
              >
                <BsSave className="btn-icon-custom" />
                {isLoading ? (
                  <>
                    Saving...
                    <span className="spinner-border spinner-border-sm align-middle ms-3"></span>
                  </>
                ) : (
                  "Save"
                )}
              </span>
              <span
                className="btn rx-btn cursor-pointer ms-3"
                onClick={() => setmodal(false)}
              >
                <SlClose className="btn-icon-custom" />
                Close
              </span>
            </Col>
          </Row>
        </Modal.Header>
        <Modal.Body className="p-0">
          <Row className="justify-content-center mt-5">
            {/* <Col xxl={6} xl={6} lg={6} sm={6} className="mb-5 text-center">
              <img
                className="profile_img"
                // src={getImage(userinfo.profileimage)}
                src={imageUrl ? imageUrl : noimage}
                onError={profileImageOnError}></img>
            </Col>
          </Row>
          <Row className="justify-content-center mt-5">
            <Col xxl={6} xl={6} lg={6} sm={6} className="mb-5 text-center">
              <label htmlFor="file-upload">
                <span className="btn rx-btn cursor-pointer">
                  Upload
                  <SlCloudUpload className="btn-icon-custom ms-2" />
                  <input
                    id="file-upload"
                    type="file"
                    onChange={handleFileInputChange1}
                    style={{ display: "none" }}
                  />
                </span>
              </label>
            </Col> */}
            <Col xxl={6} xl={6} lg={6} sm={6} className="text-center position-relative">
              {imageUrl ? (
                <img
                  src={imageUrl}
                  // onError={profileImageOnError}
                  className="profile_img"
                />
              ) : (
                <img src={usersvg} className="profile_img" alt="noimage" />
              )}
              <div className="text-center mt-2">
                <label htmlFor="profile-upload">
                  <span className="upload-user-img me-2 ">
                    <IoMdAdd className="me-1 add-icon-user" />
                    <input
                      id="profile-upload"
                      type="file"
                      onChange={handleFileInputChange1}
                      style={{ display: "none" }}
                    />
                  </span>
                </label>
                {imageUrl && (
                  <span className="upload-user-img me-2">
                    <IoClose
                      className="me-1 add-icon-user"
                      onClick={deleteimage}
                    />
                  </span>
                )}
              </div>
              {/* <label className="upload-text">Upload Photo</label> */}
            </Col>
          </Row>
          <Row>
            <Col xxl={12} xl={12} lg={12} sm={12} className="mb-5 mt-5">
              <Form.Label>User Name</Form.Label>
              {/* <input
                type="text"
                className="form-control"
                value={
                  userdetail
                    ? userdetail.first_name + " " + userdetail.last_name
                    : ""
                }
                disabled></input> */}
              <Card className="custom-card">
                <Card.Body className="px-3 py-4">
                  <span className="fs-5">
                    {userdetail
                      ? userdetail.first_name + " " + userdetail.last_name
                      : ""}
                  </span>
                </Card.Body>
              </Card>
            </Col>
            <Col xxl={12} xl={12} lg={12} sm={12} className="mb-5">
              <Form.Label>Email ID</Form.Label>
              {/* <input
                type="text"
                className="form-control"
                value={userdetail ? userdetail.email : ""}
                disabled></input> */}
              <Card className="custom-card">
                <Card.Body className="px-3 py-4">
                  <span className="fs-5 d-flex justify-content-between">
                    {userdetail ? userdetail.email : ""}
                    <a href={`mailto:${userdetail.email}`} className="cursor-pointer close-btn"><IoMailOutline size={20} /></a>
                  </span>
                </Card.Body>
              </Card>
            </Col>
            <Col xxl={12} xl={12} lg={12} sm={12} className="mb-5">
              <Form.Label>Phone</Form.Label>
              {/* <input
                type="text"
                className="form-control"
                value={formatContactInputNumber(
                  userdetail ? userdetail.phone_number : ""
                )}
                disabled></input> */}
              <Card className="custom-card">
                <Card.Body className="px-3 py-4">
                  <span className="fs-5">
                    {formatContactInputNumber(
                      userdetail ? userdetail.phone_number : ""
                    )}
                  </span>
                </Card.Body>
              </Card>
            </Col>
            {/* {userType?.displayusertype == "SUPER_ADMIN" ? ( */}
            <Col xxl={12} xl={12} lg={12} sm={12} className="mb-5">
              <Form.Label>2FA Configuration</Form.Label>
              <SingleSelectDropdown
                data={twoFAdata}
                getter={twoFactorData}
                setter={settwoFactorData}
                placeholder="Select Two Factor Authentication"
              />
            </Col>
            {/* ) : (
              ""
            )} */}
            <Col xxl={12} xl={12} lg={12} sm={12}>
              <Form.Label className="mb-3">Theme </Form.Label>
              <br />
              <ThemeModeSwitcher setIsTheme={setIsTheme} IsTheme={IsTheme} />
            </Col>
          </Row>
        </Modal.Body>
      </Modal>
    </>
  );
}

export default profile;
