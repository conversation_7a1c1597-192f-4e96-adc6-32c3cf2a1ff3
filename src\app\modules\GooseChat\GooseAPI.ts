import { APIs } from "../../serverconfig/apiURLs";
import axiosInstance, { lotoAxiosInstance } from "../../serverconfig/axiosInstance";
import encryptDecryptUtil from "../../utils/encrypt-decrypt-util";

export const goosechatlist = async (gId: any) => {
        const response = await lotoAxiosInstance.post(APIs.ALL_HEADERS.gooselist + `/${gId}`);
        return response;
}

export const goosegrouplist = async () => {
        const response = await lotoAxiosInstance.post(APIs.ALL_HEADERS.goosegrouplist);
        return response;
}

export const sendmessage = async (payload: any) => {
        const bodyparam = {
                query: payload.query,
                session_id: payload.session_id,
                client_id: "",
                groupid: payload.groupid,
                isnew: payload.isnew,
                module: payload.module
        }
        let keyinfo = JSON.parse(localStorage.keyinfo);
        const finalPayload = {
                encryptedData: encryptDecryptUtil.encryptData(
                        JSON.stringify(bodyparam),
                        keyinfo.syckey
                ),
        };
        const response = await lotoAxiosInstance.post(APIs.ALL_HEADERS.sendmsg, finalPayload);
        return response;
}

export const gooseanswers = async (payload: any) => {
        const bodyparam = {
                messageid: payload.messageid,
                reactiontype: payload.reactiontype,
                deslikemessage: payload.reactionmessage,
        }
        let keyinfo = JSON.parse(localStorage.keyinfo);
        const finalPayload = {
                encryptedData: encryptDecryptUtil.encryptData(
                        JSON.stringify(bodyparam),
                        keyinfo.syckey
                ),
        };
        const response = await lotoAxiosInstance.post(APIs.ALL_HEADERS.goosereactanswer, finalPayload);
        return response;
}

export const dislikemessage = async () => {
        const response = await lotoAxiosInstance.post(APIs.ALL_HEADERS.goosedislikemessage);
        return response;
}

export const deletegrouplist = async (gid: any) => {
        const response = await lotoAxiosInstance.post(APIs.ALL_HEADERS.goosegrouplistdelete + `/${gid}`);
        return response;
}

export const renamegrouplist = async (payload: any) => {
        const bodyparam = {
                groupid: payload.groupid,
                groupname: payload.groupname
        }
        let keyinfo = JSON.parse(localStorage.keyinfo);
        const finalPayload = {
                encryptedData: encryptDecryptUtil.encryptData(
                        JSON.stringify(bodyparam),
                        keyinfo.syckey
                ),
        };
        const response = await lotoAxiosInstance.post(APIs.ALL_HEADERS.goosegrouplistrename, finalPayload);
        return response;
}

export const goosegetgreetings = async () => {
        const response = await lotoAxiosInstance.post(APIs.ALL_HEADERS.goosegetgreetings);
        return response;
}

