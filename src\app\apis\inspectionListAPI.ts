import { createApi } from "@reduxjs/toolkit/query/react";
import { fetchRxInspectionAPI } from "../redux/fetchBase";
import { APIs } from "../serverconfig/apiURLs";
import {
  IGetInspectionTemplateByIdPayload,
  IGetInspectionTemplateByIdResponse,
  IInspectionListPayload,
  IInspectionTemplateCreatePayload,
  IInspectionTemplateListPayload,
  InspectionListItem,
  InspectionManageTemplateItem,
  InspectionReportDetails,
  InspectionReportPayloadById,
  IPaginateResponse,
  IResponse,
  TicketQuestionPayload,
  TicketQuestionResponse,
  UpdateTicketQuestCheckPayload,
  UserDetailsList,
} from "./type";

export type GetAllInspectionManageTemplateRes = IResponse<
  IPaginateResponse<InspectionManageTemplateItem[]>
>;

export type GetAllInspectionTemplateRes = IResponse<
  IPaginateResponse<InspectionListItem[]>
>;

const inspectionListApi = createApi({
  reducerPath: "inspectionList",
  baseQuery: fetchRxInspectionAPI(),
  endpoints: (builder) => ({
    getInspectionList: builder.mutation<
      GetAllInspectionTemplateRes,
      Partial<IInspectionListPayload>
    >({
      query: (payload) => {
        const sanitizePayload = Object.assign(
          {
            page: 1,
            size: 10,
            search: "",
            sortingColumns: [
              {
                sortOrder: 0,
                columnName: "",
              },
            ],
            inspectionTemplates: [],
            properties: [],
            departments: [],
            status: [],
            priorities: [],
            inspectors: [],
            ticketStatus: [],
            fromdate: "",
            todate: "",
          },
          payload
        );

        return {
          url: APIs.GET_INSPECTION_LIST,
          method: "POST",
          body: sanitizePayload || {},
        };
      },
    }),
    getInspectionManageTemplate: builder.mutation<
      GetAllInspectionManageTemplateRes,
      IInspectionTemplateListPayload
    >({
      query: (payload) => {
        const sanitizePayload = Object.assign(
          {
            page: 1,
            size: 10,
            search: "",
            sortingColumns: [
              {
                sortOrder: 0,
                columnName: "",
              },
            ],
          },
          payload
        );

        return {
          url: APIs.GET_INSPECTION_TEMPLATE_LIST, // TODO
          method: "POST",
          body: sanitizePayload || {},
        };
      },
    }),
    createInspectionTemplate: builder.mutation<
      IResponse<any>,
      IInspectionTemplateCreatePayload
    >({
      query: (payload) => ({
        url: APIs.CREATE_INSPECTION_TEMPLATE,
        method: "POST",
        body: payload,
      }),
    }),

    getInspectionTemplateById: builder.mutation<
      IResponse<IGetInspectionTemplateByIdResponse>, //TODO : Fix type
      IGetInspectionTemplateByIdPayload
    >({
      query: (payload) => ({
        url: APIs.GET_INSPECTION_TEMPLATE_BY_ID,
        method: "POST",
        body: payload,
      }),
    }),

    deleteInspectionTemplateById: builder.mutation<
      IResponse<any>, //TODO : Fix type
      IGetInspectionTemplateByIdPayload
    >({
      query: (payload) => ({
        url: APIs.DELETE_INSPECTION_TEMPLATE_BY_ID,
        method: "POST",
        body: payload,
      }),
    }),

    duplicateInspectionTemplateById: builder.mutation<
      IResponse<IGetInspectionTemplateByIdResponse>, //TODO : Fix type
      IGetInspectionTemplateByIdPayload
    >({
      query: (payload) => ({
        url: APIs.DUPLICATE_INSPECTION_TEMPLATE_BY_ID,
        method: "POST",
        body: payload,
      }),
    }),

    publishInspectionTemplateById: builder.mutation<
      IResponse<any>, //TODO : Fix type
      IGetInspectionTemplateByIdPayload
    >({
      query: (payload) => ({
        url: APIs.PUBLISH_TEMPLATE,
        method: "POST",
        body: payload,
      }),
    }),

    getInspectionReportById: builder.mutation<
      IResponse<InspectionReportDetails>,
      InspectionReportPayloadById
    >({
      query: (payload) => ({
        url: APIs.GET_INSPECTION_REPORT,
        method: "POST",
        body: payload,
      }),
    }),

    getUserDetailsByInspection: builder.mutation<
      IResponse<UserDetailsList>,
      { inspectionId: string }
    >({
      query: (payload) => ({
        url: APIs.GET_USER_DETAILS_BY_INSPECTION,
        method: "POST",
        body: payload,
      }),
    }),
    getTicketInspectionQuestion: builder.mutation<
      IResponse<TicketQuestionResponse[]>,
      TicketQuestionPayload
    >({
      query: (payload) => {
        return {
          url: APIs.GET_TICKET_INSPECTION_QUESTION,
          method: "POST",
          body: payload || {},
        };
      },
    }),
    updateTicketInspectionQuestionCheck: builder.mutation<
      IResponse<any>,
      UpdateTicketQuestCheckPayload
    >({
      query: (payload) => {
        return {
          url: APIs.UPDATE_TICKET_INSPECTION_QUESTION_CHECK,
          method: "POST",
          body: payload || {},
        };
      },
    }),
    autoSaveInspection: builder.mutation<
      IResponse<any>,
      IInspectionTemplateCreatePayload
    >({
      query: (payload) => {
        return {
          url: APIs.AUTO_SAVE_INSPECTION,
          method: "POST",
          body: payload || {},
        };
      },
    }),
  }),
});

export default inspectionListApi;

export const {
  useGetInspectionListMutation,
  useGetInspectionManageTemplateMutation,
  useCreateInspectionTemplateMutation,
  useGetInspectionTemplateByIdMutation,
  useDeleteInspectionTemplateByIdMutation,
  useDuplicateInspectionTemplateByIdMutation,
  usePublishInspectionTemplateByIdMutation,
  useGetInspectionReportByIdMutation,
  useGetUserDetailsByInspectionMutation,
  useGetTicketInspectionQuestionMutation,
  useUpdateTicketInspectionQuestionCheckMutation,
  useAutoSaveInspectionMutation,
} = inspectionListApi;
