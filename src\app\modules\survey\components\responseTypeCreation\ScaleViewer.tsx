import React, { useEffect } from 'react';
import { useFormikContext } from 'formik';
import { QuestionFormValues, generateScaleOptions } from '../addQuestions/util/constant';
import FormSelect from '../../../../component/form/FormSelect';
import FormErrorMessage from '../../../../component/form/FormErrorMessage';
import FormLabel from '../../../../component/form/FormLabel';

const ScaleViewer: React.FC = () => {
  const { values, setFieldValue } = useFormikContext<QuestionFormValues>();

  const startScaleOptions = [
    { label: '0', value: 0 },
    { label: '1', value: 1 }
  ];

  const endScaleOptions = Array.from({ length: 6 }, (_, i) => ({
    label: String(i + 5),
    value: i + 5
  }));

  // Generate options when scale values change
  useEffect(() => {
    if (values.startScale !== undefined && values.endScale !== undefined) {
      const scaleOptions = generateScaleOptions(values.startScale, values.endScale);
      setFieldValue('options', scaleOptions);
    }
  }, [values.startScale, values.endScale, setFieldValue]);

  return (
    <div className="scale-viewer">
      <div className="row">
        <div className="col-md-6 mb-3">
          <FormLabel className="form-label">Start Scale</FormLabel>
          <FormSelect
            name="startScale"
            options={startScaleOptions}
            placeholder="Select start scale"
          />
          <FormErrorMessage name="startScale" />
        </div>
        <div className="col-md-6 mb-3">
          <FormLabel className="form-label">End Scale</FormLabel>
          <FormSelect
            name="endScale"
            options={endScaleOptions}
            placeholder="Select end scale"
          />
          <FormErrorMessage name="endScale" />
        </div>
      </div>
    </div>
  );
};

export default ScaleViewer; 