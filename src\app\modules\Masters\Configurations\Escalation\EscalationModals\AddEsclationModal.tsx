import {
    closestCenter,
    DndContext,
    PointerSensor,
    useSensor,
    useSensors
} from '@dnd-kit/core';
import { Switch } from '@progress/kendo-react-inputs';
import React, { useEffect, useState } from 'react';
import { Card, Col, Modal, Row } from 'react-bootstrap';
import { FaPlus } from 'react-icons/fa';
import { MdModeEdit, MdOutlineDeleteForever, MdOutlineDragIndicator } from 'react-icons/md';
import { RxCross2 } from 'react-icons/rx';
import userImage from "../../../../../../_metronic/assets/SideMenuIcon/MenuallSVGIcon/user.svg";

import {
    arrayMove,
    SortableContext,
    useSortable,
    verticalListSortingStrategy
} from '@dnd-kit/sortable';

import { CSS } from '@dnd-kit/utilities';
import { getImage } from '../../../../../utils/CommonUtils';
import SwalMessage from '../../../../common/SwalMessage';
import { escalationService } from '../escalation.helper';
import EscalationUserModal from './EscalationUserModal';
import SingleSelectDropdown from '../../../../common/SingleSelectDropdown';

const activeData = [
    { id: 1, value: "Active" },
    { id: 0, value: "Inactive" },
]

const AddEsclationModal = ({ addEscalationModal, setAddEscalationModal, escalationId, setEscalationId, editData, userData, escalationGrid, setLoading }: any) => {
    const [selectedmemberData, setSelectedMemberData] = useState<any[]>([]);
    const [errors, setErrors] = React.useState<any>({});
    const [isFormValid, setIsFormValid] = useState(false);
    const [waitingDuration, setWaitingDuration] = useState<any>("")
    const [remainingTime, setRemainingTime] = useState<string>("");
    const [repeat, setRepeat] = useState<string>("");
    const [isApp, setisApp] = useState<boolean>(false)
    const [isCall, setisCall] = useState<boolean>(false)
    const [isSms, setisSms] = useState<boolean>(false)
    const [isEmail, setisEmail] = useState<boolean>(false)
    const [showInput, setShowInput] = useState(false);
    const [showInput1, setShowInput1] = useState(false);
    const [formattedTime, setFormattedTime] = useState<string>("");
    const [formattedTime1, setFormattedTime1] = useState<string>("");
    const [minutes, setMinutes] = useState<string>("");
    const [hours, setHours] = useState<string>("");
    const [mintueError, setMintueError] = useState<string>("");
    const [hoursError, setHoursError] = useState<string>("");
    const [minutes1, setMinutes1] = useState("");
    const [hours1, setHours1] = useState("");
    const [mintueError1, setMintueError1] = useState<string>("");
    const [hoursError1, setHoursError1] = useState<string>("");
    const [duration, setDuration] = useState("");
    const [reminderCount, setReminderCount] = useState<any>("")
    const [addEscalationUserModal, setAddEscalationUserModal] = useState<boolean>(false);
    const [active, setActive] = useState<any>()

    //start drag and drop
    const sensors = useSensors(useSensor(PointerSensor, { activationConstraint: { distance: 5 } }));

    const handleDragEnd = (event: any) => {
        const { active, over } = event;

        if (!over || active.id === over.id) return;

        const oldIndex = selectedmemberData.findIndex((item: any) => item.value === active.id);
        const newIndex = selectedmemberData.findIndex((item: any) => item.value === over.id);
        const reordered = arrayMove(selectedmemberData, oldIndex, newIndex)

        setSelectedMemberData(reordered);
    };
    const handleDelete = (memberId: string) => {
        const updatedMembers = selectedmemberData.filter((member: any) => member.value !== memberId);
        setSelectedMemberData(updatedMembers);
    };
    const SortableItem = ({ member }: { member: any }) => {
        const {
            attributes,
            listeners,
            setNodeRef,
            transform,
            transition
        } = useSortable({ id: member.value });

        const style = {
            transform: CSS.Transform.toString(transform),
            transition
        };

        return (
            <div ref={setNodeRef} style={style} {...attributes} {...listeners}>
                <Card className="mb-2 custom-card">
                    <Card.Body className="p-3">
                        <div className="d-flex align-items-center justify-content-between gap-2">
                            <div className="d-flex align-items-center gap-2">
                                <MdOutlineDragIndicator size={20} />
                                <img
                                    src={member.image ? getImage(member.image) : userImage || userImage}
                                    height={"48px"}
                                    width={"48px"}
                                    className="user-image"
                                />
                                <div className="d-flex flex-column">
                                    <span className="member-name">{member.label}</span>
                                    <span className="title">{member.title}</span>
                                </div>
                            </div>
                            <div>
                                <MdOutlineDeleteForever className="cursor-pointer" size={20} onClick={() => handleDelete(member.value)}
                                />
                            </div>
                        </div>
                    </Card.Body>
                </Card>
            </div>
        );
    };


    const updateCustomDuration = (hourValue: string, mintueValue: string) => {
        const d = hourValue || "00";
        const h = mintueValue || "00";
        setDuration(`${d.padStart(2, "0")}:${h.padStart(2, "0")}`);
    };

    const validateHours = (value: string) => {
        const numericValue = parseInt(value, 10);
        if (value === "") {
            setHoursError("");
            setHours(value);
        } else if (!isNaN(numericValue) && numericValue >= 0 && numericValue < 24) {
            setHoursError("");
            setHours(value);
        } else {
            setHoursError("Days should be between 0 and 23.");
        }
        updateCustomDuration(value, minutes);
    };
    const validateMintue = (value: string) => {
        const numericValue = parseInt(value, 10);
        if (value === "") {
            setMintueError("");
            setMinutes(value);
        } else if (
            !isNaN(numericValue) &&
            numericValue >= 0 &&
            numericValue <= 59
        ) {
            setMintueError("");
            setMinutes(value);
        } else {
            setMintueError("mintue should be between 0 and 59.");
        }
        updateCustomDuration(hours, value);
    };
    const validateHours1 = (value: string) => {
        const numericValue = parseInt(value, 10);
        if (value === "") {
            setHoursError1("");
            setHours1(value);
        } else if (!isNaN(numericValue) && numericValue >= 0 && numericValue < 24) {
            setHoursError1("");
            setHours1(value);
        } else {
            setHoursError1("Days should be between 0 and 23.");
        }
        updateCustomDuration(value, minutes);
    };
    const validateMintue1 = (value: string) => {
        const numericValue = parseInt(value, 10);
        if (value === "") {
            setMintueError1("");
            setMinutes1(value);
        } else if (
            !isNaN(numericValue) &&
            numericValue >= 0 &&
            numericValue <= 59
        ) {
            setMintueError1("");
            setMinutes1(value);
        } else {
            setMintueError1("mintue should be between 0 and 59.");
        }
        updateCustomDuration(hours, value);
    };
    // Add hours and minutes
    const handleAdd = () => {
        if (hours || minutes) {
            const formatted = `${hours || "0"}h : ${minutes || "0"}m`;
            setFormattedTime(formatted);
        }
        setShowInput(false);
    };

    const handleAdd1 = () => {
        if (hours1 || minutes1) {
            const formatted = `${hours1 || "0"}h : ${minutes1 || "0"}m`;
            setFormattedTime1(formatted);
        }
        setShowInput1(false);
    }
    //on change waiting duration
    const handleTimeChange = (e: React.ChangeEvent<HTMLInputElement>, isWaitingDuration: boolean) => {
        let value = e.target.value.replace(/\D/g, ""); // Remove all non-digits

        if (value.length > 4) value = value.slice(0, 4); // Limit to 4 digits

        // Insert colon to form HH:MM
        if (value.length >= 3) {
            value = `${value.slice(0, 2)}:${value.slice(2)}`;
        }
        if (isWaitingDuration) {
            setWaitingDuration(value);
        } else {
            setRemainingTime(value)
        }
    };

    //edit data 
    useEffect(() => {
        if (editData && addEscalationModal) {
            const userIds = editData?.users?.map((u: any) => u.userId) || [];
            const selectedMembers = userData?.filter((user: any) =>
                userIds.includes(user.value)
            );
            const sortedMembers = userIds.map((id: any) =>
                selectedMembers.find((user: any) => user.value === id)
            ).filter(Boolean); // Remove any undefined if any id is missing
            setActive(activeData?.find((item: any) => item?.id === editData?.active))
            setSelectedMemberData(sortedMembers || []);
            setisApp(editData.throughApp === 1 ? true : false);
            setisSms(editData.throughSms === 1 ? true : false);
            setisEmail(editData.throughEmail === 1 ? true : false);
            setisCall(editData.throughCall === 1 ? true : false);
            if (editData.gracePeriod) {
                const [h, m] = editData.gracePeriod.split(":");
                setHours(h || "");
                setMinutes(m || "");
                updateCustomDuration(h, m);
                setFormattedTime(`${h || "00"}h : ${m || "00"}m`);
            }
            if (editData.snoozeInterval) {
                const [h1, m1] = editData.snoozeInterval.split(":");
                setHours1(h1 || "");
                setMinutes1(m1 || "");
                updateCustomDuration(h1, m1);
                setFormattedTime1(`${h1 || "00"}h : ${m1 || "00"}m`);
            }
            setReminderCount(JSON.stringify(editData.reminderTimes) || "");
        }
    }, [editData, userData]);


    useEffect(() => {
        if (selectedmemberData?.length > 0 && (isApp || isSms || isEmail || isCall) && formattedTime && formattedTime1 && reminderCount) {
            setIsFormValid(true);
        } else {
            setIsFormValid(false);
        }
    }, [addEscalationModal, selectedmemberData, isApp, isSms, isEmail, isCall, formattedTime, formattedTime1, hours, minutes, hours1, minutes1, reminderCount]);

    const FormValidation = () => {
        const errors: any = {};

        if (selectedmemberData.length === 0) {
            errors.memberData = "Please select at least one member";
        }

        if (!isApp && !isSms && !isEmail && !isCall) {
            errors.notifyMembers = "Please select at least one notification method";
        }

        const isGraceBeforeEmpty = !hours || !minutes || (parseInt(hours) === 0 && parseInt(minutes) === 0);
        if (isGraceBeforeEmpty) {
            errors.gracePeriodbefore = "Grace period before must not be 00:00";
        }

        const isGraceAfterEmpty = !hours1 || !minutes1 || (parseInt(hours1) === 0 && parseInt(minutes1) === 0);
        if (isGraceAfterEmpty) {
            errors.gracePeriodafter = "Grace period after must not be 00:00";
        }

        if (!reminderCount || parseInt(reminderCount) === 0) {
            errors.reminderCount = "Reminder count must not be 0";
        }
        if (!active) {
            errors.status = "Please select status"
        }
        setErrors(errors);
        setIsFormValid(Object.keys(errors).length === 0);
        return Object.keys(errors).length === 0;
    };

    //handle submit 
    const handleSubmit = async () => {
        if (!FormValidation()) {
            return;
        }
        setLoading(true);
        const payload = {
            escalationId: escalationId,
            // throughCall: isCall ? 1 : 0,
            // throughSms: isSms ? 1 : 0,
            throughCall: 0,
            throughSms: 0,
            throughEmail: isEmail ? 1 : 0,
            throughApp: isApp ? 1 : 0,
            gracePeriod: `${hours.padStart(2, "0")}:${minutes.padStart(2, "0")}`,
            snoozeInterval: `${hours1.padStart(2, "0")}:${minutes1.padStart(2, "0")}`,
            reminderTimes: reminderCount,
            users: selectedmemberData.map((member: any, index: number) => ({
                sortNumber: index + 1,
                userId: member?.value
            })),
            active: active?.id
        }
        await escalationService.createEscalation(payload)
            .then((res: any) => {
                if (res?.data?.success) {
                    SwalMessage('success', res?.data?.errormsg, 'OK', 'success', false).then((isConfirm) => {
                        if (isConfirm) {
                            escalationGrid(1, "")
                            setAddEscalationModal(false);
                            setEscalationId(null);
                        }
                    })
                } else {
                    SwalMessage('error', res?.data?.errormsg, 'OK', 'error', false);
                }
            }).catch((err: any) => {
                SwalMessage('error', err?.message, 'OK', 'error', false);
            }).finally(() => {
                setLoading(false)
            })
    }
    useEffect(() => {
        if (!addEscalationModal) {
            setSelectedMemberData([]);
            setErrors({});
            setIsFormValid(false);
            setWaitingDuration("");
            setRemainingTime("");
            setRepeat("");
            setisApp(false);
            setisCall(false);
            setisSms(false);
            setisEmail(false);
            setShowInput(false);
            setShowInput1(false);
            setFormattedTime("");
            setFormattedTime1("");
            setHours("");
            setMinutes("");
            setHours1("");
            setMinutes1("");
            setMintueError("");
            setHoursError("");
            setMintueError1("");
            setHoursError1("");
            setReminderCount("");
            setEscalationId(null);
            setActive(null)
        }
    }, [addEscalationModal])

    return (
        <>
            <Modal className="modal-right p-0"
                scrollable={true}
                show={addEscalationModal}
                onHide={() => setAddEscalationModal(false)}
            >
                <Modal.Header className="border-bottom p-4 mb-5">
                    <Row className="align-items-center">
                        <Col xs={10} className="mt-auto mb-auto">
                            <h2 className="mb-0">Escalation Protocol</h2>
                        </Col>
                        <Col xs={2} className="text-end">
                            <span
                                className="close-btn cursor-pointer"
                                onClick={() =>
                                    setAddEscalationModal(false)
                                }
                            >
                                <RxCross2 fontSize={20} />
                            </span>
                        </Col>
                    </Row>
                </Modal.Header>
                <Modal.Body className="p-0">
                    <Row className='mb-3 mobile-margin'>
                        <Col sm={8} className="mt-3" >
                            <span className="title fs-2">Members</span>
                        </Col>
                        <Col sm={4} >
                            <span className="btn rx-btn w-100 mt-1" onClick={() => setAddEscalationUserModal(true)}>
                                <FaPlus className="me-2" /> <span> New</span>
                            </span>
                        </Col>
                    </Row>
                    <Row className='mb-3 mobile-margin'>
                        <Col sm={12}>
                            <div className="mb-3">
                                <div
                                // className={`${selectedmemberData?.length > 0 ? "member-list-escalation" : ""}`}
                                >
                                    {selectedmemberData && selectedmemberData.length > 0 ? (
                                        <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
                                            <SortableContext
                                                items={selectedmemberData.map((item: any) => item.value)}
                                                strategy={verticalListSortingStrategy}
                                            >
                                                {selectedmemberData.map((member: any) => (
                                                    <SortableItem key={member.value} member={member} />
                                                ))}
                                            </SortableContext>
                                        </DndContext>
                                    ) : (
                                        <div className="text-center">No members exist</div>
                                    )}
                                </div>
                            </div>
                        </Col>
                        {errors?.memberData && (
                            <Col sm={12}>
                                <span className="text-danger">{errors.memberData}</span>
                            </Col>
                        )}
                    </Row>
                    <Row className="mt-2">
                        <Col sm={12} className="mb-3 mobile-margin">
                            <span className="form-label">
                                Notify members via<span className="text-danger">*</span>
                            </span>
                        </Col>
                        <Col sm={12}>
                            <Card className="mb-2 custom-card">
                                <Card.Body className="p-3">
                                    <Row>
                                        <Col sm={6}>
                                            <span className="mx-3">
                                                <Switch
                                                    offLabel=""
                                                    onLabel=""
                                                    className="standard-switch"
                                                    checked={isApp}
                                                    onChange={() => setisApp(!isApp)}
                                                />
                                            </span>
                                            <span>In-app</span>
                                        </Col>
                                        {/* <Col sm={6}>
                                            <span className="mx-3">
                                                <Switch
                                                    offLabel=""
                                                    onLabel=""
                                                    className="standard-switch"
                                                    checked={isSms}
                                                    onChange={() => setisSms(!isSms)}
                                                />
                                            </span>
                                            <span>Text message</span>
                                        </Col> */}
                                    </Row>
                                    <Row className="mt-3">
                                        <Col sm={6}>
                                            <span className="mx-3">
                                                <Switch
                                                    offLabel=""
                                                    onLabel=""
                                                    className="standard-switch"
                                                    checked={isEmail}
                                                    onChange={() => setisEmail(!isEmail)}
                                                />
                                            </span>
                                            <span>Email</span>
                                        </Col>
                                        {/* <Col sm={6}>
                                            <span className="mx-3">
                                                <Switch
                                                    offLabel=""
                                                    onLabel=""
                                                    className="standard-switch"
                                                    checked={isCall}
                                                    onChange={() => setisCall(!isCall)}
                                                />
                                            </span>
                                            <span>Phone calls</span>
                                        </Col> */}
                                    </Row>
                                </Card.Body>
                            </Card>
                        </Col>
                        {errors?.notifyMembers && (
                            <Col sm={12}>
                                <span className="text-danger">{errors.notifyMembers}</span>
                            </Col>
                        )}
                    </Row>
                    <Row className="mt-2 mb-3 mobile-margin">
                        <Col sm={12} className="mb-2">
                            <span className="form-label">
                                Grace period before escalation
                                <span className="text-danger">*</span>
                            </span>
                        </Col>
                        <Col sm={12}>
                            <Card className="custom-card py-1 px-3">
                                <Card.Body className="p-3">
                                    <div className="d-flex align-items-center justify-content-between gap-2">
                                        <div>
                                            <span className="me-3">
                                                {formattedTime ? formattedTime : "00 : 00"}
                                            </span>
                                        </div>
                                        <div>
                                            <span
                                                className="cursor-pointer"
                                                onClick={() => setShowInput(!showInput)}>
                                                <MdModeEdit size={15} />
                                            </span>
                                        </div>
                                    </div>
                                </Card.Body>
                            </Card>
                        </Col>
                        {errors?.gracePeriodbefore && (
                            <Col sm={12}>
                                <span className="text-danger">{errors.gracePeriodbefore}</span>
                            </Col>
                        )}
                        {showInput && (
                            <Col sm={12} className="mt-4 mb-3 mobile-margin">
                                <Row className="">
                                    <Col sm={5}>
                                        <span className="form-label">
                                            Hours<span className="text-danger">*</span>
                                        </span>
                                        <input
                                            type="text"
                                            autoFocus
                                            className="form-control"
                                            placeholder="Enter Hours"
                                            value={hours}
                                            maxLength={2}
                                            onChange={(e) => validateHours(e.target.value)}
                                        />
                                        {hoursError && (
                                            <span className="text-danger">{hoursError}</span>
                                        )}
                                    </Col>
                                    <Col sm={5}>
                                        <span className="form-label">
                                            Minutes<span className="text-danger">*</span>
                                        </span>
                                        <input
                                            type="text"
                                            className="form-control"
                                            placeholder="Enter Minutes"
                                            value={minutes}
                                            maxLength={2}
                                            onChange={(e) => validateMintue(e.target.value)}
                                        />
                                        {mintueError && (
                                            <span className="text-danger">{mintueError}</span>
                                        )}
                                    </Col>
                                    <Col sm={2} className='mt-7'>
                                        <span className="btn rx-btn" onClick={() => handleAdd()} >
                                            <FaPlus />
                                        </span>
                                    </Col>
                                </Row>
                            </Col>
                        )}
                    </Row>
                    <Row className="mt-2 mb-3 mobile-margin">
                        <Col sm={12} className="mb-2">
                            <span className="form-label">
                                Grace period after every reminder
                                <span className="text-danger">*</span>
                            </span>
                        </Col>
                        <Col sm={12}>
                            <Card className="custom-card py-1 px-3">
                                <Card.Body className="p-3">
                                    <div className="d-flex align-items-center justify-content-between gap-2">
                                        <div>
                                            <span className="me-3">
                                                {formattedTime1 ? formattedTime1 : "00 : 00"}
                                            </span>
                                        </div>
                                        <div>
                                            <span
                                                className="cursor-pointer"
                                                onClick={() => setShowInput1(!showInput1)}>
                                                <MdModeEdit size={15} />
                                            </span>
                                        </div>
                                    </div>
                                </Card.Body>
                            </Card>
                        </Col>
                        {errors?.gracePeriodafter && (
                            <Col sm={12}>
                                <span className="text-danger">{errors.gracePeriodafter}</span>
                            </Col>
                        )}
                        {showInput1 && (
                            <Col sm={12} className="mt-4 mb-3 mobile-margin">
                                <Row className="align-items-end">
                                    <Col sm={5}>
                                        <span className="form-label">
                                            Hours<span className="text-danger">*</span>
                                        </span>
                                        <input
                                            type="text"
                                            autoFocus
                                            className="form-control"
                                            placeholder="Enter Hours"
                                            value={hours1}
                                            maxLength={2}
                                            onChange={(e) => validateHours1(e.target.value)}
                                        />
                                        {hoursError && (
                                            <span className="text-danger">{hoursError}</span>
                                        )}
                                    </Col>
                                    <Col sm={5}>
                                        <span className="form-label">
                                            Minutes<span className="text-danger">*</span>
                                        </span>
                                        <input
                                            type="text"
                                            className="form-control"
                                            placeholder="Enter Minutes"
                                            value={minutes1}
                                            maxLength={2}
                                            onChange={(e) => validateMintue1(e.target.value)}
                                        />
                                        {mintueError && (
                                            <span className="text-danger">{mintueError}</span>
                                        )}
                                    </Col>
                                    <Col sm={2}>
                                        <span className="btn rx-btn" onClick={() => handleAdd1()} >
                                            <FaPlus />
                                        </span>
                                    </Col>
                                </Row>
                            </Col>
                        )}
                    </Row>
                    <Row className="mt-2 mb-3 mobile-margin">
                        <Col sm={12}>
                            <span className="form-label">
                                Reminder Count<span className="text-danger">*</span>
                            </span>
                            <input
                                type="text"
                                className="form-control"
                                placeholder="Enter Reminder Count"
                                value={reminderCount}
                                maxLength={2}
                                onChange={(e) => {
                                    const value = e.target.value;
                                    // Allow only digits
                                    if (/^\d*$/.test(value)) {
                                        setReminderCount(value);
                                    }
                                }}
                            />
                        </Col>
                        {errors?.reminderCount && (
                            <Col sm={12}>
                                <span className="text-danger">{errors.reminderCount}</span>
                            </Col>
                        )}
                    </Row>
                    <Row className="mt-2 mb-3 mobile-margin">
                        <Col sm={12}>
                            <span className="form-label">
                                Status<span className="text-danger">*</span>
                            </span>
                            <SingleSelectDropdown
                                data={activeData}
                                getter={active}
                                setter={setActive}
                                placeholder="Select status"
                                textField="value"
                                dataItemKey="id"
                            />
                        </Col>
                        {errors?.status && (
                            <Col sm={12}>
                                <span className="text-danger">{errors.status}</span>
                            </Col>
                        )}
                    </Row>
                </Modal.Body>
                <Modal.Footer className="border-0 d-flex justify-content-end">
                    <button
                        className="btn rx-btn btn-black-lightblue me-4"
                        onClick={() => setAddEscalationModal(false)}
                    >
                        Cancel
                    </button>
                    <button
                        className={`btn ${isFormValid ? "btn-success" : "rx-btn"
                            }`}
                        onClick={() => handleSubmit()}
                    >
                        Save & Continue
                    </button>
                </Modal.Footer>
            </Modal>
            <EscalationUserModal
                addEscalationUserModal={addEscalationUserModal}
                setAddEscalationUserModal={setAddEscalationUserModal}
                userData={userData}
                selectedmemberData={selectedmemberData}
                setSelectedMemberData={setSelectedMemberData}
                setLoading={setLoading}
            />
        </>
    )
}

export default AddEsclationModal