import { useEffect, useState } from 'react'
import { Col, Modal, Row } from 'react-bootstrap'
import { RxCross2 } from 'react-icons/rx'
import encryptDecryptUtil from '../../../../../utils/encrypt-decrypt-util'
import SwalMessage from '../../../../common/SwalMessage'
import { permissionService } from '../Permission.helper'

const ViewModuleModal = ({ viewModuleModal, setViewModuleModal, setGridLoading, permissionId, setPermissionId, setIsViewModule, isViewModule }: any) => {
    const [moduleData, setModuleData] = useState<any[]>([]);
    const [permissionName, setPermissionName] = useState<any>("");

    const getModuleData = async (permissionId: any) => {
        setGridLoading(true)
        let keyinfo = JSON.parse(localStorage.keyinfo);
        try {
            const res = await permissionService.getmoduleandactiondropdown(permissionId);
            if (res?.data?.success) {
                const decrypted = JSON.parse(
                    encryptDecryptUtil.decryptData(res.data.data, keyinfo.syckey)
                );
                setModuleData(decrypted);
            }
        } catch (error) {
            SwalMessage(null, error, "ok", "error", false);
        } finally {
            setGridLoading(false)
        }
    };

    const getViewPermissionata = async (permissionId: any) => {
        setGridLoading(true)
        let keyinfo = JSON.parse(localStorage.keyinfo);
        try {
            const res = await permissionService.getviewpermissiondata(permissionId);
            if (res?.data?.success) {
                const decrypted = JSON.parse(
                    encryptDecryptUtil.decryptData(res.data.data, keyinfo.syckey)
                );
                setPermissionName(decrypted.permissionName);
                const groupedModules: { [key: string]: any } = {};

                decrypted.moduleActionData.forEach((mod: any) => {
                    if (!groupedModules[mod.moduleId]) {
                        groupedModules[mod.moduleId] = {
                            moduleId: mod.moduleId,
                            moduleName: mod.moduleName,
                            actionData: [],
                        };
                    }

                    // Merge actionData without duplicates
                    mod.actionData.forEach((action: any) => {
                        const alreadyExists = groupedModules[mod.moduleId].actionData.some(
                            (a: any) => a.actionId === action.actionId
                        );
                        if (!alreadyExists) {
                            groupedModules[mod.moduleId].actionData.push(action);
                        }
                    });
                });

                const formattedModules: any = Object.values(groupedModules);
                setModuleData(formattedModules);
            }
        } catch (error) {
            SwalMessage(null, error, "ok", "error", false);
        } finally {
            setGridLoading(false)
        }
    };
    useEffect(() => {
        if (viewModuleModal) {
            if (isViewModule) {
                getModuleData(permissionId);
            } else {
                getViewPermissionata(permissionId);
            }
        }
    }, [viewModuleModal]);

    //modal close state null
    useEffect(() => {
        if (!viewModuleModal) {
            setModuleData([]);
            setPermissionId("");
            setIsViewModule(false)
        }
    }, [viewModuleModal]);

    return (
        <Modal className="modal-right" show={viewModuleModal} scrollable={true}>
            <Modal.Header className="border-0 p-0 mb-4">
                <Row className="align-items-baseline">
                    <Col xs={10}><h2 className="mb-0">{isViewModule ? "View Module" : "View Permission"}</h2></Col>
                    <Col xs={2} className="text-end">
                        <span className="cursor-pointer" onClick={() => setViewModuleModal(false)}>
                            <RxCross2 fontSize={20} />
                        </span>
                    </Col>
                </Row>
            </Modal.Header>
            <Modal.Body className='p-4'>
                <Row>
                    {!isViewModule && (
                        <Col xs={12} className="mb-3 mobile-margin">
                            <span className="form-label mb-0">
                                Permission Name
                            </span>
                            <input
                                type="text"
                                className="form-control"
                                placeholder="Enter Permission Name"
                                readOnly
                                value={permissionName}
                            // onChange={(event: any) => setPermissionName(event.target.value)}
                            />
                        </Col>
                    )}
                    <Col xs={12} md={12} className="p-2">
                        {moduleData.map((item: any, index: any) => (
                            <div key={item.moduleId} className={`mb-4`}>
                                {/* Module Header with Switch */}
                                <div className="d-flex justify-content-between align-items-center mb-2">
                                    <h3 className="fw-semibold mb-0">{item.moduleName}</h3>
                                </div>

                                {/* Action Switches (Visible only if Module is Active) */}
                                <ul className="ps-8">
                                    {item.actionData.map((action: any, actionIndex: any) => {
                                        const uniqueKey = `${item.moduleId}_${action.actionId}`;
                                        const isLast = actionIndex === item.actionData.length - 1;
                                        return (
                                            <li
                                                key={uniqueKey}
                                                className={`ps-2 py-2 ${isLast ? '' : 'border-bottom'}`}
                                            >
                                                <span className='fs-6'>{action.actionName}</span>
                                            </li>
                                        );
                                    })}
                                </ul>
                            </div>
                        ))}
                    </Col>

                </Row>
            </Modal.Body>
        </Modal>
    )
}

export default ViewModuleModal
