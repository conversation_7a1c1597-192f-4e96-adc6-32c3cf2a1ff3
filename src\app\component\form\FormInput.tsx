import { Field, FieldProps } from "formik";
import React from "react";
import FormErrorMessage from "./FormErrorMessage";

interface FormInputProps {
  name: string;
  placeholder?: string;
  type?: string;
  className?: string;
  disabled?: boolean;
  readOnly?: boolean;
  rows?: number;
  as?: "input" | "textarea";
}

const FormInput: React.FC<FormInputProps> = ({
  name,
  placeholder,
  type = "text",
  className = "form-control",
  disabled = false,
  readOnly = false,
  rows,
  as = "input",
}) => {
  return (
    <div>
      <Field name={name}>
        {({ field }: FieldProps) => {
          if (as === "textarea") {
            return (
              <textarea
                {...field}
                placeholder={placeholder}
                className={className}
                disabled={disabled}
                readOnly={readOnly}
                rows={rows}
              />
            );
          }
          
          return (
            <input
              {...field}
              type={type}
              placeholder={placeholder}
              className={className}
              disabled={disabled}
              readOnly={readOnly}
            />
          );
        }}
      </Field>
      <FormErrorMessage name={name} />
    </div>
  );
};

export default FormInput;
