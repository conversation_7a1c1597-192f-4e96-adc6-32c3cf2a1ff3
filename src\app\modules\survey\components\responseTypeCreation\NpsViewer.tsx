import React, { useEffect } from 'react';
import { useFormikContext } from 'formik';
import { QuestionFormValues, generateNpsOptions } from '../addQuestions/util/constant';

const NpsViewer: React.FC = () => {
  const { setFieldValue } = useFormikContext<QuestionFormValues>();

  // Set default values and generate options when component mounts
  useEffect(() => {
    setFieldValue('startNetPromoterScore', 0);
    setFieldValue('endNetPromoterScore', 10);

    // Generate NPS options (0-10)
    const npsOptions = generateNpsOptions(0, 10);
    setFieldValue('options', npsOptions);
  }, [setFieldValue]);

  return (
    <div className="nps-viewer mt-5">
      <div className="d-flex align-items-center justify-content-between">
        <div className="text-center response-viewer-box-wrapper">
          <div className="fs-2 mb-2">0</div>
          <span>Not at all likely</span>
        </div>
        <div className="text-center response-viewer-box-wrapper">
          <div className="fs-2 mb-2">10</div>
          <span>Extremely likely</span>
        </div>
      </div>
    </div>
  );
};

export default NpsViewer; 