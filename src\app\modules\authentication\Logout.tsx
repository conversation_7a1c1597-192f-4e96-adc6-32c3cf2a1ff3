import { getToken } from "firebase/messaging";
import { messaging } from "../../../firebase/firebase";
import encryptDecryptUtil from "../../utils/encrypt-decrypt-util";
import axiosInstance from "../../serverconfig/axiosInstance";
import { APIs } from "../../serverconfig/apiURLs";
import { executeTeardown } from "../../ContextHook/LogOutContext";

const BACKEND_BASE_URL = import.meta.env.VITE_REACT_BACKEND_URL;

export const logout = async () => {
  try {
    if (!messaging) {
      throw new Error("Firebase messaging is not initialized.");
    }
    const token = await getToken(messaging);
    const request = { deviceToken: token };
    
    let keyinfo = JSON.parse(localStorage.keyinfo);
    const payload = encryptDecryptUtil.encryptData(
      JSON.stringify(request),
      keyinfo.syckey
    );
    
    await axiosInstance.post(
      `${BACKEND_BASE_URL + APIs.UNSUBSCRIBE_NOTIFICATION}`,
      { encryptedData: payload }
    );
    
    // Execute teardown before clearing storage
    executeTeardown();
    
    localStorage.clear();
    sessionStorage.clear();
    
    // Redirect using window.location (no hooks)
    window.location.href = "/login";
  } catch (error) {
    console.error("Logout error:", error);
    
    // Also try to execute teardown on error
    // executeTeardown();
    localStorage.clear();
    sessionStorage.clear();
    window.location.href = "/login";  // Redirect on error
  }
};