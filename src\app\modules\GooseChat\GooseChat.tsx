import { use<PERSON>allback, useEffect, useState } from "react";
import { <PERSON><PERSON>, ChatActionExecuteEvent, Message } from "@progress/kendo-react-conversational-ui";
import { Button } from "@progress/kendo-react-buttons";
import { FaExpandAlt, FaPlus, FaTimes } from "react-icons/fa";
import { AiOutlineDislike, AiOutlineLike, AiOutlineCopy } from "react-icons/ai";
import { Bi<PERSON>ollapse, BiTrash } from "react-icons/bi";
import { FiSidebar } from "react-icons/fi";
import { GoPencil } from "react-icons/go";
import {
  sendmessage,
  goosechatlist,
  gooseanswers,
  goosegrouplist,
  deletegrouplist,
  dislikemessage,
  goosegetgreetings,
} from "./GooseAPI";
import { Sidebar, Menu } from "react-pro-sidebar";
import { Form, Modal, OverlayTrigger, Popover, Spinner } from "react-bootstrap";
import logo from "../../../efive_assets/images/bluehalo.png";
import gooseImg from "../../../_metronic/assets/SideMenuIcon/MenuallSVGIcon/Sidebar-footer-logo.svg";
import { RootState } from "../../redux/store";
import noImage from "../../../efive_assets/images/noimage.jpg";
import { toast, ToastContainer } from "react-toastify";
import SwalMessage from "../common/SwalMessage";
import { getImage, swalMessages } from "../../utils/CommonUtils";
import { IoMdMore } from "react-icons/io";
import RenameModal from "./RenameModal";
import ReactMarkdown from "react-markdown";
import { useSelector } from "react-redux";
import remarkGfm from "remark-gfm";
import rehypeRaw from 'rehype-raw';
import { Tooltip } from "@progress/kendo-react-tooltip";
import "./GooseMedia.css";
import encryptDecryptUtil from "../../utils/encrypt-decrypt-util";
import { BeatLoader, PulseLoader } from "react-spinners";
import userSvg from "../../../_metronic/assets/SideMenuIcon/MenuallSVGIcon/user.svg";

function GooseChat({ modal, setmodal }: any) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [groupList, setGroupList] = useState<any[]>([]);
  const [chatList, setChatList] = useState<any[]>([]);
  const [collapsed, setCollapsed] = useState<boolean>(false);
  const [expand, setExpand] = useState<boolean>(false);
  const [currentSession, setCurrentSession] = useState<any>(null);
  const [selectedHistoryId, setSelectedHistoryId] = useState<any>(null);
  const [selectedGroupId, setSelectedGroupId] = useState<any>(null);
  const [likedMessages, setLikedMessages] = useState({});
  const [dislikedMessages, setDislikedMessages] = useState({});
  const [reactionType, setReactionType] = useState<any>({});
  const [copyMsg, setCopyMsg] = useState<string | null>(null);
  const [feedback, setFeedback] = useState<string | null>(null);
  const [sending, setSending] = useState<boolean>(false);
  // const [isLoading, setisLoading] = useState(false);
  const [dislikedMsg, setDislikedMsg] = useState<any[]>([]);
  const [showDislikedMsg, setShowDislikedMsg] = useState<{
    [key: string]: boolean;
  }>({});
  const [moduleType, setmoduleType] = useState<String | null | undefined>("");

  const userinfo = localStorage.userinfo
    ? JSON.parse(localStorage.userinfo)
    : null;

  const moduleTypes = [
    {
      id: 1,
      moduleName: "Document",
      value: "D",
    },
    {
      id: 2,
      moduleName: "Ticket",
      value: "T",
    },
  ];

  // ** User Details
  const { basicUserInfo }: any = useSelector((state: RootState) => state.auth);
  const user = {
    id: basicUserInfo?.userid,
    avatarUrl: `${basicUserInfo
      ? basicUserInfo.profileimage !== "null"
        ? getImage(basicUserInfo.profileimage)
        : userSvg
      : userSvg
      }`,
    avatarAltText: "user",
  };
  const bot = {
    id: 0,
    avatarUrl: gooseImg,
    avatarAltText: "goose",
  };

  // ** Chat List
  const chatlist = async (id: any) => {
    let keyinfo = JSON.parse(localStorage.keyinfo);
    setSelectedGroupId(id);
    try {
      const response = await goosechatlist(id);
      if (response?.status === 200) {
        if (response?.data?.success === true) {
          const result = encryptDecryptUtil.decryptData(
            response.data.data,
            keyinfo.syckey
          );
          const encResponse = JSON.parse(result);
          setChatList(encResponse);
          const formattedMessages = encResponse
            .map((item: any) => [
              {
                author: user,
                text: item.question,
                timestamp: new Date(),
                messageid: item.messageid,
                reactiontype: item.reactiontype,
              },
              {
                author: bot,
                text: item.answer,
                timestamp: new Date(),
                messageid: item.messageid,
                reactiontype: item.reactiontype,
                reactionmessage: item?.reactionmessage,
              },
            ])
            .flat();

          setMessages(formattedMessages);
          // Set reactions if present
          const reactions: any = {};
          formattedMessages.forEach((msg: any) => {
            reactions[msg.messageid] = msg.reactiontype;
          });
          setReactionType(reactions);
          scrollToBottom();
        } else {
          SwalMessage(null, response.data.errormsg, "Ok", "error", false);
        }
      }
    } catch (error: any) {
      console.error(error);
    }
  };

  // ** Clear messages when the modal opens
  // useEffect(() => {
  //   if (modal) {
  //     setMessages([]);
  //     setReactionType({});
  //     setSelectedHistoryId(null);
  //   }
  // }, [modal]);

  // Function to generate a new sessionId
  const generateSessionId = () => {
    const now = new Date();
    return `${now.getDate()}${now.getMonth() + 1
      }${now.getFullYear()}${now.getHours()}${now.getMinutes()}${now.getSeconds()}${now.getMilliseconds()}`;
  };

  // Check for session_id in localStorage, if null generate a new one
  let sessionId = localStorage.getItem("session_id");
  if (!sessionId) {
    sessionId = generateSessionId();
    localStorage.setItem("session_id", sessionId);
  }

  // ** Send message **
  const addNewMessage = async (event: any) => {
    setSending(true);
    let keyinfo = JSON.parse(localStorage.keyinfo);
    const userMessage = event.message;

    if (!userMessage.text.trim()) {
      toast.error("Message cannot be empty.");
      setSending(false);
      return;
    }

    setMessages([...messages, userMessage]);
    const session_id = localStorage.getItem("session_id");

    try {
      const response = await sendmessage({
        session_id: session_id,
        query: userMessage.text,
        client_id: "",
        groupid: selectedGroupId ? selectedGroupId : "",
        isnew: selectedGroupId ? false : true,
        module: moduleType,
      });

      if (response.status === 200) {
        if (response?.data?.success === true) {
          const result = encryptDecryptUtil.decryptData(
            response?.data?.data,
            keyinfo.syckey
          );
          const encResponse = JSON.parse(result);
          // Add the bot's response to the message list
          const botResponse = {
            author: bot,
            text: encResponse?.answer,
            timestamp: new Date(),
            messageid: encResponse?.messageid,
            reactiontype: encResponse?.reactiontype,
          };
          setMessages((oldMessages) => [...oldMessages, botResponse]);

          // Refresh the group list
          grouplist();

          // const updatedGroupList = await goosegrouplist();
          // if (updatedGroupList?.status === 200) {
          //   const result = encryptDecryptUtil.decryptData(
          //     updatedGroupList.data.data,
          //     keyinfo.syckey
          //   );
          //   const encResponse = JSON.parse(result);
          //   setGroupList(encResponse);

          // Set the newly created group as active if a new group was created
          if (!selectedGroupId) {
            const newGroupId = encResponse?.groupid;
            setSelectedGroupId(newGroupId);
            setSelectedHistoryId(newGroupId);
          }
          // }
          // Scroll to the bottom of the message list
          // const messageList = document.querySelector(".k-message-list");
          // if (messageList) {
          //   messageList.scrollTop = messageList.scrollHeight;
          // }
          scrollToBottom();
          setSending(false);
        } else {
          // toast.error(response?.data?.errormsg);
          SwalMessage(null, response?.data?.errormsg, "Ok", "error", false);
          setSending(false);
        }
      }
    } catch (error: any) {
      console.error(error);
      setSending(false);
    }
  };

  // Function to scroll to the bottom of the message list
  const scrollToBottom = () => {
    const messageList = document.querySelector(".k-message-list");
    if (messageList) {
      messageList.scrollTop = messageList.scrollHeight;
    }
  };
  // useEffect(() => {
  //   scrollToBottom();
  // }, [messages]);

  // ** Group List
  const grouplist = async () => {
    let keyinfo = JSON.parse(localStorage.keyinfo);
    // setisLoading(true)
    try {
      const response = await goosegrouplist();
      if (response?.status === 200) {
        if (response?.data?.success === true) {
          const responseData = response?.data;
          const result = encryptDecryptUtil.decryptData(
            responseData.data,
            keyinfo.syckey
          );
          const encResponse = JSON.parse(result);
          setGroupList(encResponse);
          // setisLoading(false);
        } else {
          setGroupList([]);
          toast.error(response?.data?.message);
          SwalMessage(null, response?.data?.message, "Ok", "error", false);
          // setisLoading(false)
        }
      }
    } catch (error: any) {
      console.error(error);
    } finally {
      // setisLoading(false)
    }
  };
  useEffect(() => {
    if (modal === true && userinfo?.isfullaccess !== 0) {
      grouplist();
      setmoduleType("D");
    }
    // grouplist();
  }, [modal]);

  // ** Sidebar and expand button toggle **
  const menuIconClick = () => {
    setCollapsed(!collapsed);
  };
  const expandIconClick = () => {
    setExpand(!expand);
  };

  // ** Like, Dislike, and Copy buttons **
  const handleLike = async (message: any) => {
    try {
      const newReactionType =
        reactionType[message.messageid] === "1" ? "0" : "1";
      const response = await gooseanswers({
        messageid: message.messageid,
        reactiontype: newReactionType,
        reactionmessage: "",
      });
      if (response.status === 200) {
        if (response.data.success === true) {
          setLikedMessages({
            ...likedMessages,
            [message.messageid]: newReactionType === "1",
          });
          setDislikedMessages({
            ...dislikedMessages,
            [message.messageid]: false,
          });
          setReactionType({
            ...reactionType,
            [message.messageid]: newReactionType,
          });
          setShowDislikedMsg({});
          setFeedback(message.messageid);
          setTimeout(() => {
            setFeedback(null);
          }, 2000);
          let keyinfo = JSON.parse(localStorage.keyinfo);
          const result = encryptDecryptUtil.decryptData(
            response.data?.data,
            keyinfo?.syckey
          );
          const parseData = JSON.parse(result);
          // console.log(parseData);
          const newchatList = chatList.map((chat: any) => {
            if (parseData?.messageid === chat?.messageid) {
              return {
                ...chat,
                reactionmessage: parseData?.deslikemessage,
              };
            } else {
              return {
                ...chat,
              };
            }
          });
          setChatList(newchatList);
          const newMessages = messages.map((msg: any) => {
            if (
              parseData?.messageid === msg?.messageid &&
              msg?.author?.id === 0
            ) {
              return {
                ...msg,
                reactionmessage: parseData?.deslikemessage,
              };
            } else {
              return {
                ...msg,
              };
            }
          });
          setMessages(newMessages);
        } else {
          SwalMessage(null, response.data.message, "Ok", "error", false);
        }
      }
    } catch (error: any) {
      console.error(error);
    }
  };

  const handleDislike = async (message: any, dismsg: any) => {
    try {
      const newReactionType = "-1";
      // reactionType[message.messageid] === "-1" ? "0" : "-1";
      const response = await gooseanswers({
        messageid: message.messageid,
        reactiontype: newReactionType,
        reactionmessage: dismsg,
      });
      if (response.status === 200) {
        if (response.data.success === true) {
          setLikedMessages({ ...likedMessages, [message.messageid]: false });
          setDislikedMessages({
            ...dislikedMessages,
            [message.messageid]: newReactionType === "-1",
          });
          setReactionType({
            ...reactionType,
            [message.messageid]: newReactionType,
          });
          setShowDislikedMsg({});
          setFeedback(message.messageid);
          setTimeout(() => {
            setFeedback(null);
          }, 2000);
          let keyinfo = JSON.parse(localStorage.keyinfo);
          const result = encryptDecryptUtil.decryptData(
            response.data?.data,
            keyinfo?.syckey
          );
          const parseData = JSON.parse(result);
          const newchatList = chatList.map((chat: any) => {
            if (parseData?.messageid === chat?.messageid) {
              return {
                ...chat,
                reactionmessage: parseData?.deslikemessage,
              };
            } else {
              return {
                ...chat,
              };
            }
          });
          setChatList(newchatList);
          const newMessages = messages.map((msg: any) => {
            if (
              parseData?.messageid === msg?.messageid &&
              msg?.author?.id === 0
            ) {
              return {
                ...msg,
                reactionmessage: parseData?.deslikemessage,
              };
            } else {
              return {
                ...msg,
              };
            }
          });
          setMessages(newMessages);
        } else {
          SwalMessage(null, response.data.errormsg, "Ok", "error", false);
        }
      }
    } catch (error: any) {
      console.error(error);
    }
  };

  const handledislikemsg = async (messageId: any) => {
    setShowDislikedMsg((prevState) => ({
      ...prevState,
      [messageId]: !prevState[messageId],
    }));

    let keyinfo = JSON.parse(localStorage.keyinfo);
    try {
      const messages = await dislikemessage();
      if (messages?.status === 200) {
        if (messages?.data?.success === true) {
          const responseData = messages?.data;
          const result = encryptDecryptUtil.decryptData(
            responseData.data,
            keyinfo.syckey
          );
          const encResponse = JSON.parse(result);
          setDislikedMsg(encResponse?.message);
        } else {
          SwalMessage(null, messages?.data?.errormsg, "Ok", "error", false);
        }
      }
    } catch (error) {
      console.error(error);
    }
  };

  const handleCopy = (message: any) => {
    // navigator.clipboard.writeText(message.text);
    const extractTextWithoutLinks = (text: string) => {
      const regex = /\[([^\]]+)\]\([^)]+\)/g;
      // return text.replace(regex, "");
      let plainText = text.replace(regex, "");

      const regexListNumbers = /^\d+\.\s+/gm;
      plainText = plainText.replace(regexListNumbers, "");

      return plainText;
    };

    const plainText = extractTextWithoutLinks(message.text);
    if (message.messageid) {
      navigator.clipboard.writeText(plainText).then(() => {
        setCopyMsg(message.messageid);
        setTimeout(() => {
          setCopyMsg(null);
        }, 2000);
      });
    }
  };

  // ** Delete Group list **
  const handleDeleteGrouplist = async (gid: any, gname: any) => {
    let keyinfo = JSON.parse(localStorage.keyinfo);
    const confirm = await SwalMessage(
      `Delete ${gname}?`,
      swalMessages?.text?.deteleGooseChatGrouplist,
      "Delete",
      "error",
      true
    );
    if (confirm) {
      try {
        const response = await deletegrouplist(gid);
        if (response?.status === 200) {
          if (response?.data?.success === true) {
            SwalMessage(null, response?.data?.errormsg, "Ok", "success", false);
          } else {
            SwalMessage(null, response?.data?.errormsg, "Ok", "error", false);
          }

          // Fetch the updated group list
          grouplist();

          // const updatedGroupListResponse = await goosegrouplist();
          // if (updatedGroupListResponse?.status === 200) {
          //   const result = encryptDecryptUtil.decryptData(
          //     updatedGroupListResponse?.data?.data,
          //     keyinfo.syckey
          //   );
          //   const encResponse = JSON.parse(result);
          //   setGroupList(encResponse);

          // Check if the deleted group was the currently active group
          if (gid === selectedGroupId) {
            setSelectedGroupId("");
            setSelectedHistoryId("");
            setMessages([]);
          }
          // }
        }
      } catch (error) {
        console.error("Error deleting group:", error);
      }
    }
  };

  // ** Rename Group list **
  const [renameModal, setRenameModal] = useState(false);
  const [renamingGroup, setRenamingGroup] = useState<{
    groupid: any;
    groupname: string;
  } | null>(null);

  const handleRenameGrouplist = (gid: any, gname: any) => {
    setRenameModal(true);
    setRenamingGroup({ groupid: gid, groupname: gname });
  };

  // ** Start new chat **
  // const startNewChat = async () => {
  //   // setMessages([]);
  //   // setSelectedGroupId(null);
  //   // setCurrentSession(new Date().getTime());
  //   // setSelectedHistoryId(null);

  //   let keyinfo = JSON.parse(localStorage.keyinfo);
  //   try {
  //     const response = await goosegetgreetings();
  //     console.log("response", response)
  //     if (response?.status === 200) {
  //       if (response?.data?.success === true) {
  //         const responseData = response?.data;
  //         const result = encryptDecryptUtil.decryptData(
  //           responseData.data,
  //           keyinfo.syckey
  //         );
  //         const encResponse = JSON.parse(result);
  //         const quickReplies = encResponse?.data?.quick_replies || [];

  //         // Set quick replies state
  //         setQuickReplies(quickReplies);
  //         const formattedMessages =
  //         {
  //           author: bot,
  //           text: encResponse?.data?.answer,
  //           timestamp: new Date(),
  //           messageid: '',
  //           reactiontype: '',
  //           reactionmessage: '',
  //         }

  //         const quick = quickReplies.map((text: string) => ({
  //           author: bot,
  //           text,
  //           timestamp: new Date(),
  //           messageid: '',
  //           reactiontype: '',
  //           reactionmessage: '',
  //         }));

  //        setMessages([...formattedMessages, ...quick]);
  //         setSelectedGroupId(null);
  //         setCurrentSession(new Date().getTime());
  //         setSelectedHistoryId(null);
  //         console.log("encResponse-->", encResponse?.data);
  //       } else {
  //         // toast.error(response?.data?.errormsg);
  //         SwalMessage(null, response?.data?.errormsg, "Ok", "error", false);
  //       }
  //     }
  //   } catch (error: any) {
  //     console.error(error);
  //   }
  // };
  const startNewChat = async () => {
    let keyinfo = JSON.parse(localStorage.keyinfo);
    try {
      const response = await goosegetgreetings();
      if (response?.status === 200 && response?.data?.success === true) {
        const responseData = response?.data;
        const result = encryptDecryptUtil.decryptData(
          responseData.data,
          keyinfo.syckey
        );
        const encResponse = JSON.parse(result);
        const quickReplies = encResponse?.data?.quick_replies || [];

        const quick = [
          {
            author: bot,
            text: encResponse?.data?.answer,
            suggestedActions: quickReplies.map((text: string) => ({
              type: "reply",
              value: text
            })),
            hideActions: true,
          }
        ];
        setMessages(quick);

        setSelectedGroupId(null);
        setCurrentSession(new Date().getTime());
        setSelectedHistoryId(null);
      } else {
        SwalMessage(null, response?.data?.errormsg, "Ok", "error", false);
      }
    } catch (error: any) {
      console.error(error);
    }
  };


  // ** handleHistoryClick on chat
  const handleHistoryClick = (item: any) => {
    if (messages.length > 0 && currentSession) {
      const currentQuestion = messages.find(
        (msg) => msg?.author?.id === user?.id
      )?.text;
      if (currentQuestion) {
        setChatList([
          ...chatList,
          {
            question: currentQuestion,
            answer: "",
            timestamp: new Date(),
            session: currentSession,
          },
        ]);
      }
    }
    chatlist(item?.groupid);
    setSelectedGroupId(item?.groupid);
    setSelectedHistoryId(item?.groupid);
    setCurrentSession(item?.session || new Date().getTime());
    setReactionType({});
    if (window.innerWidth <= 1200) {
      setCollapsed(true);
    }
  };

  // ** React markdown link
  const LinkRenderer = ({ href, children }: any) => {
    const isHttps = href && href.startsWith("https");
    return (
      <Tooltip className="" position="bottom">
        <a
          className=" goose-chat-link"
          title={children}
          href={href}
          target={isHttps ? "_blank" : "_self"}
          rel="noopener noreferrer"
        >
          {children}
        </a>
      </Tooltip>
    );
  };

  // ** render message
  const renderMessage = (props: any) => {
    const { item } = props;
    return (
      <div
        className={`k-message ${item?.author?.id === bot?.id ? "k-message-received" : "k-message-sent"
          }`}
      >
        <div className="k-message-content">
          <div className="k-chat-message-box p-3">
            <div className={`k-message-text ${item.hideActions ? "quick" : ""}`}>
              <ReactMarkdown
                components={{
                  a: LinkRenderer,
                }}
                remarkPlugins={[remarkGfm]}
                rehypePlugins={[rehypeRaw]}
              >
                {item.text}
              </ReactMarkdown>
            </div>
            {item?.author?.id === bot?.id && !item?.hideActions && (
              <>
                <div className="k-message-actions mt-2">
                  <span
                    className={`k-button k-flat `}
                    onClick={() => handleLike(item)}
                  >
                    <AiOutlineLike
                      className={`${reactionType[item?.messageid] === "1"
                        ? "like-active"
                        : ""
                        }`}
                    />
                    {feedback === item?.messageid &&
                      reactionType[item?.messageid] === "1" && (
                        <div className="popover-message">
                          Thank you for your feedback!
                        </div>
                      )}
                  </span>
                  <span
                    className={`k-button k-flat `}
                    onClick={() => handledislikemsg(item?.messageid)}
                  >
                    <AiOutlineDislike
                      className={`${reactionType[item?.messageid] === "-1"
                        ? "dislike-active"
                        : ""
                        }`}
                    />
                    {feedback === item?.messageid &&
                      reactionType[item?.messageid] === "-1" && (
                        <div className="popover-message">
                          Thank you for your feedback!
                        </div>
                      )}
                  </span>
                  <span
                    className="k-button k-flat"
                    onClick={() => handleCopy(item)}
                  >
                    <AiOutlineCopy />
                    {copyMsg === item.messageid && (
                      <div className="popover-message">Copied!</div>
                    )}
                  </span>
                </div>

                {/* Dislike message */}
                {showDislikedMsg[item.messageid] ? (
                  <div className="p-2 feedback-options">
                    <div className="d-flex align-items-center justify-content-between mb-3">
                      <span>Tell us more:</span>
                      <span
                        className="cursor-pointer"
                        onClick={() => setShowDislikedMsg({})}
                      >
                        <FaTimes size={18} />
                      </span>
                    </div>
                    <div className="d-flex flex-wrap">
                      {dislikedMsg?.map((msg: any, index: any) => (
                        <span
                          className={`dislike-option`}
                          onClick={() => handleDislike(item, msg)}
                          key={index}
                        >
                          {msg}
                        </span>
                      ))}
                    </div>
                  </div>
                ) : null}

                {item?.reactionmessage && (
                  <span className="d-block ms-auto text-danger k-w-fit dislike-option mt-1">
                    {item?.reactionmessage}
                  </span>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    );
  };

  // send quick reply msg type
  const handleActionExecute = (event: ChatActionExecuteEvent) => {
    const { action } = event;
    if (action.type === "reply") {
      addNewMessage({ message: { author: user, text: action.value } });
    } else if (action.type === "alert") {
      alert(action.value);
    } else if (action.type === "openUrl") {
      window.open(action.value, "_blank");
    } else if (action.type === "call") {
      window.location.href = `tel:${action.value}`;
    }
  };


  // ** Responsive chat
  useEffect(() => {
    if (window.innerWidth <= 1200) {
      setCollapsed(true);
    }
    if (window.innerWidth <= 768) {
      setExpand(true);
    }
  }, []);

  useEffect(() => {
    if (!modal && window.innerWidth <= 1200) {
      setCollapsed(true);
    }
    if (!modal && window.innerWidth > 1200) {
      setCollapsed(false);
    }
    if (!modal) {
      setExpand(false);
    } else if (window.innerWidth <= 768) {
      setExpand(true);
    }
  }, [modal]);

  const handleSize = useCallback(() => {
    setCollapsed(window.innerWidth <= 1200);
    setExpand(window.innerWidth <= 768);
  }, [collapsed, expand]);

  useEffect(() => {
    window.addEventListener("resize", handleSize);
    return () => {
      window.removeEventListener("resize", handleSize);
    };
  }, [handleSize]);

  return (
    <>
      {/* {isLoading && <Spinner />} */}
      {userinfo?.isfullaccess === 0 ? (
        <Modal
          className={`modal-right-halfs`}
          show={modal}
        >
          <Modal.Body className="p-0">
            <div style={{ textAlign: "center", marginTop: "40%" }}>
              <div>
                <p className="welcome-text-page mb-2">You are not authorized.</p>
                <p className="fs-3">Please contact administration.</p>
              </div>
            </div>
          </Modal.Body>
          <Modal.Footer className="border-0 p-0 ">
            <button
              className={`btn rx-btn`}
              onClick={() => setmodal(false)}>
              Close
            </button>
          </Modal.Footer>
        </Modal>
      ) : (
        <Modal
          className={`${expand ? "modal-right-full" : "modal-right-halfs"}`}
          show={modal}
        // scrollable={true}
        // onHide={() => setmodal(false)}
        >
          <Modal.Header>
            <div className="header-chat">
              {expand ? (
                <>
                  <div className="goose-logo">
                    <img src={logo} alt="logo" width={100} />
                  </div>
                  <div className="goose-title">
                    <h4 className="mb-0">You're talking to Goose</h4>
                  </div>
                  <div className="user-img">
                    <span
                      onClick={expandIconClick}
                      className="p-2 cursor-pointer fs-1 expand-btn"
                    >
                      <BiCollapse />
                    </span>
                    <img
                      src={
                        basicUserInfo
                          ? basicUserInfo?.profileimage
                            ? getImage(basicUserInfo?.profileimage)
                            : noImage
                          : noImage
                      }
                      alt="user-img"
                      width={35}
                      height={35}
                      className="rounded-circle mx-2 mx-sm-4 user-img-header"
                    />
                    <span
                      className="cursor-pointer ms-2 fs-4"
                      onClick={() => {
                        setCollapsed(true);
                        setmodal(false);
                      }}
                    >
                      <FaTimes />
                    </span>
                  </div>
                </>
              ) : (
                <>
                  <div className="goose-logo d-flex align-items-center">
                    <img src={gooseImg} alt="logo" width={40} />
                    <h4 className="ms-3 mb-0">You're talking to Goose</h4>
                  </div>
                  <div className="user-img d-block d-sm-flex align-items-center">
                    <span
                      onClick={expandIconClick}
                      className="p-2 cursor-pointer fs-4"
                    >
                      <FaExpandAlt />
                    </span>
                    <span
                      className="cursor-pointer ms-2 fs-4"
                      onClick={() => setmodal(false)}
                    >
                      <FaTimes />
                    </span>
                  </div>
                </>
              )}
            </div>
          </Modal.Header>
          <Modal.Body className="p-0">
            <div className="goose-chat">
              <div className="sidebar-wrapper">
                <Sidebar
                  className="custom-sidebar"
                  collapsed={collapsed}
                  transitionDuration={800}
                >
                  <div className="logo-section d-flex align-items-center justify-content-between">
                    <Button
                      onClick={startNewChat}
                      className={`btn btn-dark ${collapsed ? "d-none" : "d-block"
                        }`}
                    >
                      New Chat <FaPlus />
                    </Button>
                    <div className="btn btn-dark" onClick={menuIconClick}>
                      <FiSidebar />
                    </div>
                  </div>

                  <div
                    className={`menu-wrapper ${collapsed ? "d-none" : "d-block"}`}
                  >
                    <Menu>
                      {groupList?.map((data: any) => {
                        return (
                          <div key={data?.grouplistname}>
                            <h6 className="history-date">
                              {data?.grouplistname}
                            </h6>
                            {data?.grouplist?.map((item: any) => {
                              return (
                                <div
                                  key={item?.groupname}
                                  className={`history-item mb-2`}
                                  onClick={() => handleHistoryClick(item)}
                                >
                                  <Tooltip
                                    position="bottom"
                                    anchorElement="target"
                                    style={{
                                      width:
                                        item?.groupname?.length > 50
                                          ? "20%"
                                          : "auto",
                                    }}
                                  >
                                    <div
                                      className={`history-text  ${item?.groupid === selectedHistoryId
                                        ? "active"
                                        : ""
                                        }`}
                                    >
                                      <span
                                        className="group-name"
                                        title={item?.groupname}
                                      >
                                        {/* {item?.groupname?.length > 23 ? (
                                    <> {item?.groupname?.slice(0, 24)} ...</>
                                  ) : (
                                    <>{item?.groupname}</>
                                  )} */}
                                        {item?.groupname}
                                      </span>
                                      <div className="group-action">
                                        <a>
                                          <IoMdMore className="td-icon" />
                                        </a>
                                        <div className="group-action-dropdown">
                                          <p
                                            className="delete-grouplist mb-1"
                                            onClick={() =>
                                              handleDeleteGrouplist(
                                                item?.groupid,
                                                item?.groupname
                                              )
                                            }
                                          >
                                            <BiTrash className="me-2" /> Delete
                                          </p>
                                          <p
                                            className="edit-grouplist mb-0"
                                            onClick={() =>
                                              handleRenameGrouplist(
                                                item?.groupid,
                                                item?.groupname
                                              )
                                            }
                                          >
                                            <GoPencil className="me-2" /> Rename
                                          </p>
                                        </div>
                                      </div>
                                    </div>
                                  </Tooltip>
                                </div>
                              );
                            })}
                          </div>
                        );
                      })}
                    </Menu>
                  </div>
                </Sidebar>
              </div>
              <div className={`chat-container `}>
                <Chat
                  user={user}
                  messages={messages}
                  onMessageSend={addNewMessage}
                  // onActionExecute={handleActionExecute}
                  placeholder={"Type a message..."}
                  width={"100%"}
                  messageTemplate={renderMessage}
                  className={` ${expand
                    ? "k-chat-100"
                    : collapsed
                      ? "collapsed-true"
                      : "collapsed-false"
                    }`}
                />
                {sending && (
                  <span className="goose-send-msg-loader">
                    {/* <Spinner animation="grow" size="sm" />
                    <Spinner animation="grow" size="sm" />
                    <Spinner animation="grow" size="sm" /> */}
                    Thinking
                    <BeatLoader size={5} color="gray" />
                  </span>
                )}
              </div>
            </div>
            {/* <div className="d-flex gap-2 align-items-center mt-2">
              <Form.Label className="m-0">Module:</Form.Label>
              {moduleTypes.map((item) => (
                <>
                  <input
                    className="text-white"
                    key={item.id}
                    type="radio"
                    name="module"
                    id={`module-${item.id}`}
                    value={item.value}
                    onChange={(e) => setmoduleType(e.target.value)}
                    checked={moduleType == item.value}
                  />
                  <label
                    htmlFor={`module-${item.id}`}
                    className="user-select-none cursor-pointer"
                  >
                    {item.moduleName}
                  </label>
                </>
              ))}
            </div> */}
          </Modal.Body>
          <span className="goose-mistakes-text">
            Goose uses AI and can make mistakes. Check important information.
          </span>

          <ToastContainer
            position="top-right"
            autoClose={2000}
            hideProgressBar={false}
            newestOnTop={false}
            closeOnClick
            rtl={false}
            pauseOnFocusLoss
            draggable
            pauseOnHover={false}
            theme="light"
          />
        </Modal>
      )}
      <RenameModal
        renameModal={renameModal}
        setRenameModal={setRenameModal}
        renamingGroup={renamingGroup}
        grouplist={grouplist}
      />
    </>
  );
}

export default GooseChat;
