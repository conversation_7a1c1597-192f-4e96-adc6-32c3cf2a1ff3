import { GridColumn as Column, Grid } from "@progress/kendo-react-grid";
import { Tooltip } from '@progress/kendo-react-tooltip';
import { useEffect, useState } from 'react';
import { Badge, Dropdown } from "react-bootstrap";
import { FaQrc<PERSON>, FaReg<PERSON>ye, FaRegUserCircle } from 'react-icons/fa';
import { IoMdAdd, IoMdMore } from 'react-icons/io';
import { MdOutlineEdit } from "react-icons/md";
import encryptDecryptUtil from "../../../../utils/encrypt-decrypt-util";
import SwalMessage from "../../../common/SwalMessage";
import { permissionService } from "./Permission.helper";
import AddPermissionModal from "./PermissionModal/AddPermissionModal";
import PermissionAssignMember from "./PermissionModal/PermissionAssignMember";
import ViewModuleModal from "./PermissionModal/ViewModuleModal";
import { escalationService } from "../Escalation/escalation.helper";
import { ViewPermissionMembers } from "./PermissionModal/ViewPermissionMember";
import Spinner from "../../../common/Spinner";
import { useBreadcrumbContext } from "../../../../../_metronic/layout/components/header/BreadcrumbsContext";
import { orderBy } from "@progress/kendo-data-query";


function Permission() {
    const [addPermissionModal, setAddPermissionModal] = useState(false)
    const [viewModuleModal, setViewModuleModal] = useState<boolean>(false);
    const [isViewModule, setIsViewModule] = useState<boolean>(false);
    const [viewMemberModal, setViewMemberModal] = useState<boolean>(false);
    const [userData, setUserData] = useState<any[]>([]);
    const [addPermissionUserModal, setAddpermissionUserModal] = useState<boolean>(false);
    const [timeoutId, setTimeoutId] = useState<any>();
    const [permissionId, setPermissionId] = useState<any>(null);
    const [isEdit, setIsEdit] = useState<boolean>(false);
    const [gridLoading, setGridLoading] = useState<boolean>(false)
    const initialSort: Array<any> = [
        { field: "permission Name", dir: "asc" },
    ];
    const [sort, setSort] = useState(initialSort);
    const [data, setdata] = useState<any[]>([]);
    const [gridTotalCount, setGridTotalCount] = useState<number>(0);

    //breadcrumb 
    const { setLabels } = useBreadcrumbContext();
    useEffect(() => {
        setLabels([{ path: "", state: {}, breadcrumb: "" }]);
        setLabels([{ path: "", state: {}, breadcrumb: "Permissions" }]);
        // setLabels(["Safety Forms / "]);
    }, []);

    const renderswitch = (id: any, data: any) => {
        const status = data?.active ? "Active" : "Inactive";
        const background =
            status === "Active"
                ? "success"
                : "danger"
        return (
            <td className="k-table-td">
                <Badge
                    style={{ width: "90px" }}
                    bg={background}
                    className="text-white ellipsis-cell"
                >
                    {status}
                </Badge>
            </td>
        );
    };

    const getPermissionGridData = async (page: any) => {
        setGridLoading(true);
        try {
            let keyinfo = JSON.parse(localStorage.keyinfo);
            await permissionService.getPermissionGridData(page, page?.size, "", "1")
                .then((res: any) => {
                    if (res?.data?.success) {
                        const result: any = encryptDecryptUtil.decryptData(
                            res?.data?.data,
                            keyinfo.syckey
                        );
                        const parsedData = JSON.parse(result);
                        setdata(parsedData?.data);
                        setGridTotalCount(parsedData?.totalCount);
                        setGridLoading(false);
                    } else {
                        SwalMessage(null, res?.data?.errormsg, "Ok", "error", false);
                        setGridLoading(false);
                    }
                })
        } catch (error: any) {
            SwalMessage(null, error?.message, "Ok", "error", false);
            setGridLoading(false);
        } finally {
            setGridLoading(false);
        }
    }

    useEffect(() => {
        const pageNumber = Math.floor(page.skip / page.take) + 1;
        if (timeoutId) {
            clearTimeout(timeoutId);
        }
        const id = setTimeout(() => {
            getPermissionGridData(pageNumber);
        }, 500);
        setTimeoutId(id);
        return () => {
            clearTimeout(id);
        };
    }, []);
    // Pagination start
    const initialDataState: any = { skip: 0, take: 10 };
    const [page, setPage] = useState<any>(initialDataState);
    const [pageSizeValue, setPageSizeValue] = useState<
        number | string | undefined
    >();
    const pageChange = (event: any) => {
        const targetEvent = event.targetEvent as any;
        const take =
            targetEvent.value === "All" ? data.length : event.page.take;

        if (targetEvent.value) {
            setPageSizeValue(targetEvent.value);
        }
        setPage({
            ...event.page,
            take,
        });
    };

    // Pagination End

    // handle user
    const handleAssignMember = async (permissionId: any, isAssign: any) => {
        setGridLoading(true);
        try {
            let keyinfo = JSON.parse(localStorage.keyinfo);
            const res = await permissionService.getmembersforpermission(permissionId, isAssign, "");
            if (res?.data?.success) {
                const decrypted = JSON.parse(
                    encryptDecryptUtil.decryptData(res.data.data, keyinfo.syckey)
                );
                setUserData(decrypted);
                setPermissionId(permissionId);
                if (isAssign === 0) {
                    setViewMemberModal(true);
                } else {
                    setAddpermissionUserModal(true);
                }
            } else {
                SwalMessage(null, res?.data?.errormsg, "Ok", "error", false);
            }
        } catch (error: any) {
            SwalMessage(null, error?.message, "Ok", "error", false);
        } finally {
            setGridLoading(false);
        }
    }

    const renderaction = ({ content }: any) => {
        return (
            <td className="k-table-td">

                <Dropdown className="new-chat-btn" style={{ position: 'static' }}>
                    <Dropdown.Toggle
                        as="span"
                        className="fs-1 cursor-pointer ms-2"
                    >
                        <IoMdMore className="td-icon cursor-pointer" />
                    </Dropdown.Toggle>
                    <Dropdown.Menu align="end">
                        <Dropdown.Item onClick={() => { setIsEdit(true), setPermissionId(content?.permissionId), setAddPermissionModal(true) }}>
                            <span className="fs-5">
                                <MdOutlineEdit className="me-4" size={18} />
                                Edit
                            </span>
                        </Dropdown.Item>
                        <Dropdown.Item onClick={() => { setPermissionId(content?.permissionId), setViewModuleModal(true), setIsViewModule(false) }} >
                            <span className="fs-5">
                                <FaRegEye className="me-4" size={16} />
                                View
                            </span>
                        </Dropdown.Item>
                        <Dropdown.Item onClick={() => handleAssignMember(content?.permissionId, 1)}>
                            <span className="fs-5">
                                <FaRegUserCircle className="me-4" size={18} />
                                Assign Members
                            </span>
                        </Dropdown.Item>
                    </Dropdown.Menu>
                </Dropdown>
            </td>
        );
    };
    // end action

    //  start tooltip
    const renderTooltipCell = (props: any) => {
        const { dataItem, field, content } = props;
        if (field === "permissionName") {
            const ISBadge = dataItem?.isDefault == 1 ? (
                // <span
                //     className={`badge rounded-circle d-inline-block  me-2 ticket-number-bage Rx-btn-bg`}
                // >
                //     {"D"}
                // </span>
                <span className={`badge ticket-number-bage Rx-btn-bg`}>
                    <i>Default</i>
                </span>
            ) : null;
            return (
                <td className='k-table-td'>
                    <span className="d-inline-flex align-items-center">
                        <span className='ellipsis-cell me-2' title={content}>{dataItem[field]}</span>
                        {ISBadge ? ISBadge : ""}
                        {/* {ISBadge ? (
                            ISBadge
                        ) : (
                            <span
                                style={{
                                    display: "inline-block",
                                    width: 19,
                                    height: 18,
                                    marginRight: "0.5rem", // same as .me-2
                                }}
                            />
                        )} */}
                    </span>
                </td>)
        }
        if (field === "memberCount") {
            return (
                <td className='k-table-td'>
                    <span className='ellipsis-cell text-center cursor-pointer' onClick={() => handleAssignMember(dataItem?.permissionId, 0)} >{dataItem[field] || 0}</span>
                </td>
            );
        }
        if (field === "moduleCount") {
            return (
                <td className='k-table-td'>
                    <span className='ellipsis-cell text-center cursor-pointer' onClick={() => { setPermissionId(dataItem?.permissionId), setViewModuleModal(true), setIsViewModule(true) }}>{dataItem[field] || 0}</span>
                </td>
            )
        }
        return (
            <td className='k-table-td'>
                <span className='ellipsis-cell' title={content}>{dataItem[field]}</span>
            </td>
        );
    };

    // end tooltip

    return (
        <>
            {gridLoading && <Spinner />}
            <div className='row pageheader mb-7'>
                <div className=' col-xl-6 col-lg-6 col-sm-6 mt-auto mb-auto'>
                    <h1 className='page-title mobile-margin mb-0'>
                        Permission
                    </h1>
                </div>
                <div className=" col-xl-6 col-lg-8 col-sm-6 mt-auto mb-auto text-end">
                    <input
                        type="search"
                        className="form-control d-none" //form-control search-box mobile-margin
                        placeholder="Search..."
                    />
                    {/* <Link
                        to={""}
                        className="btn rx-btn ms-3 mobile-margin mb-lg-3"

                    >
                        <IoFilter className="btn-icon-custom" />
                        Filter{" "}
                    </Link>
                    <Link to={""} className="btn rx-btn ms-3 mobile-margin mb-lg-3" >
                        <FaFileDownload className="btn-icon-custom" />

                        Export
                    </Link> */}
                    <span className="btn rx-btn ms-3 mobile-margin mb-lg-3" onClick={() => { setIsEdit(false), setAddPermissionModal(true) }}>
                        <IoMdAdd className="btn-icon-custom" />
                        Add Permission
                    </span>
                </div>
            </div >
            <div className='card mt-0'>
                <div className='card-body p-0'>
                    <div className='table_div' style={{ width: '100%' }}>
                        <Tooltip position="bottom" anchorElement="target">
                            <Grid
                                data={orderBy(data, sort)}
                                skip={page.skip}
                                take={page.take}
                                total={gridTotalCount}
                                pageable={{
                                    buttonCount: 4,
                                    pageSizes: [5, 10, 15, "All"],
                                    pageSizeValue: pageSizeValue,
                                    // responsive:true,
                                }}

                                onPageChange={pageChange}
                                sortable={true}
                                sort={sort}
                                onSortChange={(e: any) => {
                                    setSort(e.sort);
                                }}
                            >
                                <Column title='Permission Name' field='permissionName' cell={(props) => renderTooltipCell({ ...props, content: props.dataItem.permissionName })} />
                                <Column title='Member' field='memberCount' headerClassName="center-header" cell={(props) => renderTooltipCell({ ...props, content: props.dataItem.memberCount })} />
                                <Column title='Module' field='moduleCount' headerClassName="center-header" cell={(props) => renderTooltipCell({ ...props, content: props.dataItem.moduleCount })} />
                                <Column title='Active' field='active' width={"200px"} cell={(props) =>
                                    renderswitch(
                                        props.dataItem.id,
                                        props.dataItem
                                    )
                                } />
                                <Column title='Action' width={120} cell={(props) => renderaction({ ...props, content: props.dataItem })} />
                            </Grid>
                        </Tooltip>
                    </div>
                </div>
            </div>
            <AddPermissionModal
                addPermissionModal={addPermissionModal}
                setAddPermissionModal={setAddPermissionModal}
                setGridLoading={setGridLoading}
                getPermissionGridData={getPermissionGridData}
                permissionId={permissionId}
                setPermissionId={setPermissionId}
                isEdit={isEdit}
                setIsEdit={setIsEdit}
            />
            <ViewModuleModal
                viewModuleModal={viewModuleModal}
                setViewModuleModal={setViewModuleModal}
                setGridLoading={setGridLoading}
                permissionId={permissionId}
                setPermissionId={setPermissionId}
                setIsViewModule={setIsViewModule}
                isViewModule={isViewModule}
            />
            <PermissionAssignMember
                addPermissionUserModal={addPermissionUserModal}
                setAddpermissionUserModal={setAddpermissionUserModal}
                permissionId={permissionId}
                userData={userData}
                setUserData={setUserData}
                setPermissionId={setPermissionId}
                getPermissionGridData={getPermissionGridData}
                setLoading={setGridLoading}
            />
            <ViewPermissionMembers
                viewMemberModal={viewMemberModal}
                setViewMemberModal={setViewMemberModal}
                permissionId={permissionId}
                setPermissionId={setPermissionId}
                userData={userData}
                setUserData={setUserData}
                setLoading={setGridLoading}
            />
        </>
    )
}

export default Permission;