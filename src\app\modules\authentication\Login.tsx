import { useEffect, useState } from "react";
import { Placement } from "react-bootstrap/esm/types";
import { FaEye, FaEyeSlash, FaRegQuestionCircle } from "react-icons/fa";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { <PERSON><PERSON><PERSON>oader } from "react-spinners";
import validator from "validator";
import { useThemeMode } from "../../../_metronic/partials";
import logo11 from "../../../efive_assets/images/Login_Logo11.svg";
import logo from "../../../efive_assets/images/Logo.png";
import { getSecurityDetails, login } from "../../redux/authSlice";
import { useAppDispatch } from "../../redux/store";
import { getUserDetail } from "../../redux/userSlice";
// import { BACKEND_BASE_URL } from "../../serverconfig/constants";
import encryptDecryptUtil from "../../utils/encrypt-decrypt-util";
import SwalMessage from "../common/SwalMessage";
import LoginSidePanal from "./LoginSidePanal";
import { useIntiAllService } from "../../../serviceRegister";
const BACKEND_BASE_URL = import.meta.env.VITE_REACT_BACKEND_URL;

const Login = () => {
  const [email, setemail] = useState("");
  const [password, setpassword] = useState("");
  const [showpassword, setshowpassword] = useState(false);
  const [error, setError] = useState<any>({});
  const [serverMsg, setServerMsg] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isLoginFailed, setIsLoginFailed] = useState(false);
  const [placement, setPlacement] = useState<Placement | undefined>(
    "bottom-start"
  );
  const [footerInfo, setFooterInfo] = useState<any>();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { initAllService } = useIntiAllService();
  const location = useLocation();
  useEffect(() => {
    if (location.state?.email) {
      setemail(location.state.email);
    }
  }, [location.state?.email]);
  const spinner = (
    <div className="spinner-page">
      <ClipLoader size={60} className="spinner" />
    </div>
  );


  // ** Email Validation
  const validateEmail = (email: any) => {
    if (!email.trim()) {
      return "Email is required";
    } else if (!validator.isEmail(email)) {
      return "Email is invalid";
    } else if (!validator.isEmail(email)) {
      return "Email is invalid";
    } else {
      return "";
    }
  };

  // ** Password Validation
  const validatePassword = (password: any) => {
    if (!password.trim()) {
      return "Password is required";
    } else {
      return "";
    }
  };

  // ** onChange Email event
  const handleEmailChange = (e: any) => {
    const value = e.target.value;
    setemail(value?.toLowerCase());
    if (value) {
      setError((prevError: any) => ({
        ...prevError,
        email: validateEmail(value),
      }));
    } else {
      setError((prevError: any) => ({
        ...prevError,
        email: "",
      }));
    }
  };

  // ** onChange Password event
  const handlePasswordChange = (e: any) => {
    const value = e.target.value;
    setpassword(value);
  };

  // ** placement of popover
  useEffect(() => {
    const publicRoutes = [
      "/termsandconditions",
      "/getstarted",
      "/forgotpassword",
      "/setnewpassword",
      "/twoFactorEmailAuthentication",
      "twofactorauthentication",
    ];
    const isLogin = JSON.parse(localStorage.getItem("islogin") as string);
    const userDetails = JSON.parse(
      localStorage.getItem("userdetail") as string
    );
    const lastVisitedPath: any = localStorage.getItem("lastVisitedPath");
    const isPublicRoute = publicRoutes.includes(lastVisitedPath);
    if (isLogin == undefined || userDetails == undefined || isPublicRoute) {
      navigate("/login");
    } else {
      navigate(lastVisitedPath);
    }
    const handleResize = () => {
      const screenWidth = window.innerWidth;
      if (screenWidth >= 768) {
        setPlacement("bottom-start");
      } else {
        setPlacement("auto");
      }
    };

    handleResize();
    window.addEventListener("resize", handleResize);

    return () => window.removeEventListener("resize", handleResize);
  }, []);
  // ** onSubmit event
  const handleSubmit = async (e: any) => {
    e.preventDefault();
    setIsLoading(true);
    let keyinfo = JSON.parse(localStorage.keyinfo);
    const emailError = validateEmail(email);
    const passwordError = validatePassword(password);
    if (!emailError && !passwordError) {
      if (email && password) {
        const data = {
          username: email?.trim(),
          password: password,
        };
        try {
          const response = await dispatch(login(data)).unwrap();
          if (response && response.success) {
            console.log("response", response)
            setIsLoading(false);
            setIsLoginFailed(false);

            // * Register All service
            initAllService();

            let resultData = response.data;
            let userData = JSON.parse(
              encryptDecryptUtil.decryptData(resultData, keyinfo.syckey)
            );
            sessionStorage.setItem("permission", userData?.permission);
            console.devAPILog("userData decrypt--------------------", userData);
            if (userData.isFirstTimeLogin == true) {
              navigate("/termsandconditions", { state: { email: email } });
            } else if (userData.twofactorflag == "yes") {
              await dispatch(
                getUserDetail({ userid: userData.userid })
              ).unwrap().then((response: any) => {
                let userData = response.data;
                let decryptedData = encryptDecryptUtil.decryptData(userData, keyinfo.syckey);
                const parseData = JSON.parse(decryptedData)
                if (parseData?.isMobile === 1) {
                  navigate("/twofactorauthentication");
                } else if (parseData?.isMobile === 0) {
                  navigate("/twoFactorEmailAuthentication", { state: { email: parseData?.email, isForgot: false } })
                }
              })
              setServerMsg(resultData.errormsg);
            } else if (userData.twofactorflag == "no") {
              setServerMsg(resultData.errormsg);
              localStorage.setItem("islogin", "true");
              sessionStorage.setItem("islogin", "true");
              await dispatch(
                getUserDetail({ userid: userData.userid })
              ).unwrap();
              const userType: any = localStorage.getItem("userinfo");
              const newUserType = JSON.parse(userType);
              if (newUserType?.profileimage) {
                const imageUrl =
                  BACKEND_BASE_URL +
                  "resources/files" +
                  newUserType?.profileimage;
                sessionStorage.setItem("imageUrl", imageUrl);
              }
              sessionStorage.setItem("fullName", newUserType.displayusername);
              sessionStorage.setItem("emailId", newUserType.displayemail);
              sessionStorage.setItem("userId", newUserType.userid);

              if (userData?.displayusertype === "SUPER_ADMIN") {
                navigate("/admindashboard");
                // navigate(0);
              } else {
                // sessionId
                const now = new Date();
                const sessionId = `${now.getDate()}${now.getMonth() + 1
                  }${now.getFullYear()}${now.getHours()}${now.getMinutes()}${now.getSeconds()}${now.getMilliseconds()}`;
                localStorage.setItem("session_id", sessionId);

                navigate("/dashboard");
                if (userData?.themeid !== undefined) {
                  if (userData?.themeid == 0) {
                    updateMenuMode("light");
                    updateMode("light");
                    localStorage.setItem("kt_theme_mode_menu", "light");
                    localStorage.setItem("kt_theme_mode_value", "light");
                  } else if (userData?.themeid == 1) {
                    updateMenuMode("dark");
                    updateMode("dark");
                    localStorage.setItem("kt_theme_mode_menu", "dark");
                    localStorage.setItem("kt_theme_mode_value", "dark");
                  } else if (userData?.themeid == 2) {
                    updateMenuMode("system");
                    updateMode("system");
                    localStorage.setItem("kt_theme_mode_menu", "system");
                    localStorage.setItem("kt_theme_mode_value", "system");
                  }
                }
                // navigate(0);
              }
            }
          } else {
            SwalMessage(null, response?.errormsg, "Ok", "error", false);
            setIsLoading(false);
            setIsLoginFailed(true);
          }
        } catch (error: any) {
          SwalMessage(null, error?.message, "Ok", "error", false);
          setIsLoading(false);
        } finally {
          setIsLoading(false);
        }
      }
    } else {
      setIsLoading(false);
      setError({
        email: emailError,
        password: passwordError,
      });
    }
  };

  const handleInitialSecurityData = async () => {
    setIsLoading(true);
    try {
      const response = await dispatch(getSecurityDetails()).unwrap();
      const reponseData: any = response;
      if (reponseData && reponseData.success == true) {
        if (localStorage.getItem("keyinfo")) {
          localStorage.removeItem("keyinfo");

          localStorage.setItem("keyinfo", JSON.stringify(reponseData.data));
          setFooterInfo(reponseData?.data);
        } else {
          localStorage.setItem("keyinfo", JSON.stringify(reponseData.data));
          setFooterInfo(reponseData?.data);
        }
        setIsLoading(false);
      } else {
        setIsLoading(false);
        SwalMessage(
          null,
          "Something Went Wrong. Please Try Again",
          "Ok",
          "error",
          false
        );
      }
    } catch (error: any) {
      setIsLoading(false);
      SwalMessage(null, error.message, "Ok", "error", false);
    } finally {
      setIsLoading(false);
    }
  };

  const { mode, updateMode, updateMenuMode } = useThemeMode();

  useEffect(() => {
    updateMode("system");
    updateMenuMode("system");
    handleInitialSecurityData();
    // try {
    //   const darkModeMediaQuery = window.matchMedia(
    //     "(prefers-color-scheme: dark)"
    //   );
    //   const theme = darkModeMediaQuery.matches ? "dark" : "light";
    //   if (theme === "dark") {
    //     const images = document.getElementsByClassName("logo-image-mobile");
    //     if (images.length > 0) {
    //       const image = images[0] as HTMLImageElement;
    //       image.src = logo11;
    //     }
    //   }
    // } catch (error) {
    //   console.error("Error checking prefers-color-scheme:", error);
    // }
  }, []);
  const currentYear = new Date().getFullYear();
  const backendString = footerInfo?.projectcopyright || "";
  const updatedString = backendString
    ? backendString.replace(/\d{4}|XXXX/, currentYear.toString()) // Replace year if string exists
    : `© ${currentYear} BLUEHALO`;
  return (
    <>
      {isLoading && spinner}
      <div className="d-flex flex-column flex-lg-row flex-column-fluid login-bg login-page-height">
        <LoginSidePanal pagelabel="" />
        <div className="d-flex flex-column flex-lg-row-fluid p-10 login-right ">
          <div className="d-flex flex-center flex-column flex-lg-row-fluid">
            <Link to={""} className="need-help-text">
              <FaRegQuestionCircle className="need-help-icon" />
              Need Help?
            </Link>
            <div className="form-width p-10 mobile-padding">
              <form className="form w-100" onSubmit={handleSubmit}>
                <div className="text-center mobile-logo-div mb-3">
                  <img src={logo} className="logo-image-mobile" />
                </div>
                <div className="text-center mb-2 mb-lg-15 mb-md-6">
                  <span className="right-main-header">Login</span>
                </div>
                <div className="mb-2 mb-md-5">
                  <label className="form-label">Email</label>
                  <input
                    type="text"
                    className="form-control "
                    placeholder="Enter your email"
                    value={email}
                    onChange={handleEmailChange}
                    maxLength={60}
                  />
                  {error.email && (
                    <p className="text-danger mt-1">{error.email}</p>
                  )}
                  {/* <span className="error-msg">Wrong email address</span> */}
                </div>
                <div className="mb-2 mb-md-5">
                  <label className="form-label ">Password</label>

                  <div className="position-relative">
                    <input
                      type={showpassword ? "text" : "password"}
                      className="form-control  position-relative"
                      placeholder="Enter Password"
                      onChange={handlePasswordChange}
                      maxLength={16}
                    />
                    {error.password && (
                      <p className="text-danger mt-1">{error.password}</p>
                    )}
                    <span
                      className="pw_hide_show mt-1"
                      onClick={() => setshowpassword(!showpassword)}
                    >
                      {showpassword ? (
                        <FaEye color="#ffffff" />
                      ) : (
                        <FaEyeSlash color="#ffffff" />
                      )}
                    </span>
                  </div>
                  <div className="d-flex mt-3">
                    {/* <Link to={""} className="forgot-text " >Forgot Username?</Link> */}
                    <Link
                      to={"/forgotpassword"}
                      className="forgot-text ms-auto"
                    >
                      Forgot password?
                    </Link>
                  </div>
                </div>
                <div className="fv-row mb-5 mt-5">
                  <button
                    type="submit"
                    className={`btn rx-btn d-block w-100 ${email !== "" && password !== "" ? "" : "disabled"
                      }`}
                  >
                    Login
                  </button>
                </div>
                {/* <div className="text-center">
                      <span className="sign-up-text">Don't have an account? <Link  to={""} className="forgot-text " >Sign Up</Link></span>
                      </div> */}
              </form>
              <div>
                {error.general && (
                  <p className="text-danger mt-1 text-center">
                    <span style={{ whiteSpace: "pre-line" }}>
                      {error.general}
                    </span>
                  </p>
                )}
              </div>
            </div>
          </div>
          <div className="d-flex flex-center flex-wrap">
            <div className="text-center right-footer">
              <span>Don’t have an account?</span>
              <Link to={"/getstarted"} className="right-footer-text">
                Get Started
              </Link>
            </div>
          </div>
          <div className="flex-center flex-wrap px-5 mt-3  inlogin-footre-version">
            <span className="left-footer-text fw-semibold">
              {/* {footerInfo ? footerInfo?.projectcopyright : " "} */}
              {updatedString}
            </span>
            <span className="left-footer-text ms-4">
              {footerInfo ? footerInfo?.projectversion : ""}
            </span>
          </div>
        </div>
      </div>
    </>
  );
};

export default Login;
