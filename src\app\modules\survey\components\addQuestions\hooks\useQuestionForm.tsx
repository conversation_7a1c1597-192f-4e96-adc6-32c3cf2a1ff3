import * as Yup from 'yup'
import { AttachmentType, QuestionFormValues, ResponseType } from '../util/constant'

export const responseViewerFelid = {
  options: [],
  // Temporary fields for UI components - these will be used to generate options
  ratting: 5,
  rattingIcon: 'star',
  startNetPromoterScore: 0,
  endNetPromoterScore: 10
}

export const initialValues: QuestionFormValues = {
  questionText: '',
  responseType: ResponseType.MULTIPLE_CHOICE,
  allowAttachment: false,
  attachmentType: [],
  allowComment: false,
  autoTicketGeneration: false,
  allowBranching: false,
  isRequired: false,
  // Default values for response types
 ...responseViewerFelid
}

export const questionFormSchema = Yup.object().shape({
  questionText: Yup.string().required('Question title is required'),
  responseType: Yup.string().required('Response type is required'),
  allowAttachment: Yup.boolean().required(),
  attachmentType: Yup.array()
    .of(Yup.mixed<AttachmentType>().oneOf(['IMAGE', 'VIDEO', 'AUDIO']))
    .when('allowAttachment', {
      is: true,
      then: (schema) => schema.min(1, 'Select at least one attachment type').required('Attachment type is required'),
      otherwise: (schema) => schema.notRequired(),
    }),
  allowComment: Yup.boolean().required(),
  autoTicketGeneration: Yup.boolean().required(),
  allowBranching: Yup.boolean().required(),
  isRequired: Yup.boolean().required(),
  // Response type specific validations
  // Options validation - will be populated dynamically based on response type
  options: Yup.array()
    .when('responseType', {
      is: (type: ResponseType) => [
        ResponseType.MULTIPLE_CHOICE,
        ResponseType.YES_NO,
        ResponseType.THUMBS_UP_DOWN,
        ResponseType.EMOJI
      ].includes(type),
      then: (schema) => schema.min(1, 'At least one option is required').required('Options are required'),
      otherwise: (schema) => schema.notRequired(),
    }),

  // Temporary fields for UI - these will be used to generate options array
  ratting: Yup.number()
    .when('responseType', {
      is: ResponseType.RATING,
      then: (schema) => schema
        .required('Max rating is required')
        .min(1, 'Max rating must be at least 1')
        .max(10, 'Max rating must be at most 10'),
      otherwise: (schema) => schema.notRequired(),
    }),
  rattingIcon: Yup.string()
    .when('responseType', {
      is: ResponseType.RATING,
      then: (schema) => schema.required('Rating icon is required'),
      otherwise: (schema) => schema.notRequired(),
    }),
  // Temporary fields for UI - these will be used to generate options array
  startNetPromoterScore: Yup.number()
    .when('responseType', {
      is: ResponseType.NET_PROMOTER_SCORE,
      then: (schema) => schema.required('Start NPS is required').equals([0], 'Start NPS must be 0'),
      otherwise: (schema) => schema.notRequired(),
    }),
  endNetPromoterScore: Yup.number()
    .when('responseType', {
      is: ResponseType.NET_PROMOTER_SCORE,
      then: (schema) => schema.required('End NPS is required').equals([10], 'End NPS must be 10'),
      otherwise: (schema) => schema.notRequired(),
    }),
})

const useQuestionForm = () => {
  return {
    initialValues,
    questionFormSchema,
  }
}

export default useQuestionForm